const stripe = require('../../lib/stripe').stripe;

async function voidInvoices(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }
  console.log('voidInvoices job started');

  let numProcessed = 0;
  let numVoided = 0;
  let numErrors = 0;
  for await (const invoice of stripe.invoices.list({ status: 'open' })) {
    const createdAt = invoice.created * 1000;
    const numMillsecondsElapsed = Date.now() - createdAt;
    const numHoursElapsed = numMillsecondsElapsed / 3600000;
    console.log(`voidInvoices processing invoice ${invoice.id}, createdAt: ${new Date(createdAt)}, numHoursElapsed: ${numHoursElapsed}`);
    if (numHoursElapsed >= 24) {
      console.log(`voidInvoices voiding invoice ${invoice.id}`);
      try {
        await stripe.invoices.voidInvoice(invoice.id);
        numVoided++;
      } catch (err) {
        console.log(`Error voiding invoice ${invoice.id}, error: ${err}, invoice: ${invoice}`);
        numErrors++;
      }
    }
    numProcessed++;
  }

  console.log(`voidInvoices job finished, numProcessed: ${numProcessed}, numVoided: ${numVoided}, numErrors: ${numErrors}`);
  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

module.exports = {
  voidInvoices,
};
