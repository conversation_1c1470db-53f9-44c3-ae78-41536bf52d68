const { expect, assert } = require('chai');
const request = require('supertest');
const { DateTime } = require('luxon');
const sinon = require('sinon');
const {
  app, mongoose, validAudioPath, validImagePath, initialCoins, createUser, getProfile, waitMs, initSocket, destroySocket, getSocketPromise
} = require('./common');
const {
  initApp, setNotificationSettings, getUserProfile, blockUser, getMyProfile,
  setFirstName,setUserDescription,setUserWork,setUserEducation,setUserProfilePromptAnswers, setPersonality,
  sendUserLike, approveUser, sendMessage, postQuestion, postQuestionAnonymously, postComment, setUserGender, setLocation, uploadUserPicture,
  setEnneagram,setUserBirthday, getWebDbProfile
} = require('./helper/api');
const { getDeletedUids, fakeSES, setYotiMockResponseFake, setReplicateMockResponse, setMockImageModerationResponse, reset, waitFor, notifs } = require('./stub');
const basic = require('../lib/basic');
const User = require('../models/user');
const Question = require('../models/question');
const Profile = require('../models/profile');

//skip all test, as the feature is archived
/*
describe('Anonymous Profile', async () => {
  it('set anonymousProfileNickname, user version below 1.13.74', async () => {
    let res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ appVersion: '1.13.73'});
        expect(res.status).to.equal(200);
        expect(res.body.user.anonymousProfileNickname).to.be.undefined;

    res = await request(app)
      .put('/v1/user/anonymousProfileNickname')
      .set('authorization', 0)
      .send({ anonymousProfileNickname: 'Nickname 0'});
    expect(res.status).to.equal(404);
  })

  it('set anonymousProfileNickname', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    let res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ appVersion: '1.13.74'});
        expect(res.status).to.equal(200);
        expect(res.body.user.anonymousProfileNickname).to.be.undefined;

    res = await request(app)
      .put('/v1/user/anonymousProfileNickname')
      .set('authorization', 0)
      .send({ anonymousProfileNickname: ' '});
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/anonymousProfileNickname')
      .set('authorization', 0)
      .send({ anonymousProfileNickname: 'Me'});
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: 0})
    expect(user.anonymousProfileNickname).to.equal('Me')

    res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 1)
          .send({ appVersion: '1.13.74'});
        expect(res.status).to.equal(200);
        expect(res.body.user.anonymousProfileNickname).to.be.undefined;

    res = await request(app)
      .put('/v1/user/anonymousProfileNickname')
      .set('authorization', 1)
      .send({ anonymousProfileNickname: 'Me'});
    expect(res.status).to.equal(422);

    user = await User.findOne({_id: 0})
    expect(user.anonymousProfileNickname).to.equal('Me')

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.74'});
    expect(res.status).to.equal(200);
    expect(res.body.user.anonymousProfileNickname).to.equal('Me');
    console.log('res.body: ', res.body)
  })

  it('post question Anonymously', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    for (let uid = 0; uid < 3; uid++){
      let attributes
      if(uid === 0){
        // init user0 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);
    }

    // user0 send like to user2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user 2 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/anonymousProfileNickname')
      .set('authorization', 0)
      .send({ anonymousProfileNickname: 'Nickname 0'});
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 0)
      .send({
        mbti: 'ESTJ'
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    await setEnneagram(0,{enneagram:'1w2'});

    const postedAnonymouslyBy = {
      anonymousProfileNickname: 'Nickname 0',
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'title1 ',
      text: 'anonymous post',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // console.log('res body post questions: ', res)
    expect(res.body.postedAnonymouslyBy).to.deep.equal(postedAnonymouslyBy)

    //user0 numQuestionsPostedAnonymously should increase
    user0 = await User.findOne({_id: 0})
    expect(user0.metrics.numQuestionsPostedAnonymously).to.equal(1)

    //user1 should no receive notification since app_339 false
    // res = await request(app)
    //   .get('/v1/notification')
    //   .set('authorization', 1);
    // expect(res.status).to.equal(200);
    // console.log('notification res.body: ', JSON.stringify(res.body, null, 2))
    // expect(res.body.notifications[0].profile).to.equal(null);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs.numSent :', notifs.numSent)
    // notification should only 3 (new-friend-request, new-match, new-karma-level), no notification related to post
    expect(notifs.numSent).to.equal(3);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.not.equal('mentioned-in-post');

    reset();

    params = {
      interestName: 'kpop',
      title: 'title2 ',
      text: 'normal post',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '0',
          firstName: 'name 0',
        },
      ]
    }

    // user1 try to post Anonymously should failed, due of app_339 false
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('Anonymous profile not found');

    // user2 try to post Anonymously should failed, due of os web
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send(params);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('Anonymous profile not found');

    params = {
      interestName: 'kpop',
      title: 'title1 ',
      text: 'text1',
      mentionedUsersTitle: [
        {
          _id: '0',
          firstName: 'name 0',
        },
      ]
    }

    // user1 create post should success
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;


    // user0 can see both questions (anonymous & normal post) in feed
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    // expect(res.body.questions[0]._id).to.eql(q1Id);
    expect(res.body.questions.length).to.eql(2);
    anonymousQuestion = res.body.questions.find((question) => question._id == q1Id)
    normalQuestion = res.body.questions.find((question) => question._id == q2Id)
    expect(anonymousQuestion.postedAnonymouslyBy).to.deep.equal(postedAnonymouslyBy)
    expect(normalQuestion.profilePreview._id).to.deep.equal('1')
    expect(normalQuestion.profilePreview.firstName).to.deep.equal('name 1')
    console.log('res body get questions: ', res.body.questions)

    //user1 can't see the anonymous post
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1)
    anonymousQuestion = res.body.questions.find((question) => question._id == q1Id)
    normalQuestion = res.body.questions.find((question) => question._id == q2Id)
    expect(anonymousQuestion).to.be.undefined
    expect(normalQuestion.profilePreview._id).to.deep.equal('1')
    expect(normalQuestion.profilePreview.firstName).to.deep.equal('name 1')
    console.log('res body get questions: ', res.body.questions)

    //user2 can't see the anonymous post
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1)
    anonymousQuestion = res.body.questions.find((question) => question._id == q1Id)
    expect(anonymousQuestion).to.be.undefined
    console.log('res body get questions: ', res.body.questions)

    //user0 create normal Question mention user1
    params = {
      interestName: 'kpop',
      title: 'title1 ',
      text: 'normal post',
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q3Id = res.body._id;

    //user0 get his anonymous post
    res = await request(app)
      .get('/v1/user/anonymousQuestions')
      .query({ anonymousProfileNickname: 'Nickname 0' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1)
    console.log('res body get: ', JSON.stringify(res.body, null, 2))

    //user0 get his normal post
    res = await request(app)
      .get('/v1/user/questions')
      .query({ createdBy: '0' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1)
    console.log('res body get: ', JSON.stringify(res.body, null, 2))

    //user1 get user0 anonymous post
    res = await request(app)
      .get('/v1/user/anonymousQuestions')
      .query({ anonymousProfileNickname: 'Nickname 0' })
      .set('authorization', 1);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('get anonymous post not allowed');


    //user2 get user0 anonymous post
    res = await request(app)
      .get('/v1/user/anonymousQuestions')
      .query({ anonymousProfileNickname: 'Nickname 0' })
      .set('authorization', 2);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('get anonymous post not allowed');

    // post image
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
        postedAnonymously: true,
      });
    expect(res.status).to.equal(200);
    const qImageId = res.body._id;

    // finish uploading image
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: qImageId })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    // last recent notification analyticsLabel should not equal new-comment-on-post-matches or new-comment-on-post-other-souls
    console.log('notifs: ', notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.not.equal('friend-posted');
    reset()

    //user 0 create anonymous post mention user 2
    params = {
      interestName: 'kpop',
      title: 'title anonymous ',
      text: 'anonymous post',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '2',
          firstName: 'name 2',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q4Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    // last recent notification analyticsLabel should not equal new-comment-on-post-matches or new-comment-on-post-other-souls
    console.log('notifs: ', notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('mentioned-in-post');
    expect(notifs.recent.notification.title).to.equal('Nickname 0 mentioned you in a post');
    reset()
  })

  it('post comment Anonymously', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);


    // structure
    // Q1 anonym post
    //   - c0 anonym comment
    // q2 normal post
    //   - c2 anonym comment L1
    //     - c2R1
    //   - c3 normal comment L1
    //     - c3R1 anonym reply L2
    //      - c3R1R1 normal reply l3
    //     - c3R2 normal reply L2
    //      - c3R2R1 normal reply l3


    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }

    const user0AnonymousProfile = {
      anonymousProfileNickname: `Nickname 0`,
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user0 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user2 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'anonymous post',
      text: 'text1',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // console.log('res body post questions: ', res)
    expect(res.body.postedAnonymouslyBy).to.deep.equal(user0AnonymousProfile)

    // res = await request(app)
    //   .put('/v1/ai/social')
    //   .set('authorization', 3)
    //   .send({
    //     questionId: q1Id,
    //     outputType: 'suggest', // 'suggest' or 'paraphrase' or 'proofread'
    //     userInput: 'text',
    //   });
    // expect(res.status).to.equal(200);

    //user0 numQuestionsPostedAnonymously should increase
    user0 = await User.findOne({_id: 0})
    expect(user0.metrics.numQuestionsPostedAnonymously).to.equal(1)

    const user3AnonymousProfile = {
      anonymousProfileNickname: 'Nickname 3',
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user3 comment anonynomously on anonynomous post, no notification related comment should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'this is a test anonynomous comment !',
        parentId: q1Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;
    console.log('post anonynomous comment: ', res.body)

    expect(res.body.postedAnonymouslyBy).to.deep.equal(user3AnonymousProfile)

    //check anonymousUsersThatCommented
    question1 = await Question.findOne({_id: q1Id})
    expect(question1.anonymousUsersThatCommented.length).to.equal(1)
    expect(question1.usersThatCommented.length).to.equal(0)
    expect(question1.anonymousUsersThatCommented).to.deep.equal(['3'])
    expect(question1.numUsersThatCommented).to.equal(1)
    // console.log('question1: ', question1)

    //user3 numQuestionsPostedAnonymously should increase
    user3 = await User.findOne({_id: 3})
    expect(user3.metrics.numCommentsPostedAnonymously).to.equal(1)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    // last recent notification analyticsLabel should not equal new-comment-on-post-matches or new-comment-on-post-other-souls
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-other-souls');
    expect(notifs.recent.notification.title).to.be.equal('anonymous post');
    expect(notifs.recent.notification.body).to.be.equal(`Nickname 3: @Nickname 0 this is a test anonynomous comment !`);
    reset()
    //user0 check get notification, profile should be null if the comment was for anonym
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log('notification res.body: ', res.body)
    expect(res.body.notifications[0].profile).to.equal(null);


    // in actual anonymous post should not seen by user1 since user1 is false group
    //user1 try to comment anonymously should due of failed app_339 not set
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'this is a test anonynomous comment !',
        parentId: q1Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('comment anonymously not allowed');

    //user2 try to comment anonymously should failed due of os web
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'this is a test anonynomous comment !',
        parentId: q1Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('comment anonymously not allowed from web');

    //user1 create Question normaly
    params = {
      interestName: 'kpop',
      title: 'normal post',
      text: 'text1',
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;
    reset()

    //user3 get allquestion should see 0 numComment on mormal post
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2)
    normalQuestion = res.body.questions.find((question) => question._id == q2Id)
    console.log('res body get questions: ', res.body.questions)
    expect(normalQuestion.numComments).to.equal(0);

    // user3 comment anonynomously on normal post, no notification related comment should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a test anonynomous comment on normal post !',
        parentId: q2Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);
    const c2Id = res.body._id;
    console.log('post anonynomous comment: ', res.body)

    expect(res.body.postedAnonymouslyBy).to.deep.equal(user3AnonymousProfile)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ',notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-karma-level');
    expect(notifs.numSent).to.be.equal(1);
    reset()
    //user1 check get notification after c2, profile should be null due of the comment was for anonym
    // res = await request(app)
    //   .get('/v1/notification')
    //   .set('authorization', 1);
    // expect(res.status).to.equal(200);
    // console.log('user1 notification after c2 res.body: ', res.body)
    // expect(res.body.notifications[0].profile).to.equal(null);

    // user0 reply anonynomous comment anonynomously on normal post
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q2Id,
        text: 'this is a test reply anonynomous comment anonymously on normal post !',
        parentId: c2Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);
    const c2R1Id = res.body._id;
    console.log('c2R1: ', res.body)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    // last recent notification analyticsLabel should not equal new-comment-on-post-matches or new-comment-on-post-other-souls
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-karma-level');
    expect(notifs.numSent).to.be.equal(1);
    reset()

    question2 = await Question.findOne({_id: q2Id})
    expect(question2.anonymousUsersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented.length).to.equal(0)
    expect(question2.anonymousUsersThatCommented).to.deep.equal(['3','0'])
    expect(question2.numUsersThatCommented).to.equal(2)

    // user3 normal comment on normal post,
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a test comment on normal post !',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c3Id = res.body._id;
    console.log('c3: ', res.body)

    question2 = await Question.findOne({_id: q2Id})
    expect(question2.anonymousUsersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented.length).to.equal(1)
    expect(question2.usersThatCommented).to.deep.equal(['3'])
    expect(question2.anonymousUsersThatCommented).to.deep.equal(['3', '0'])
    expect(question2.numUsersThatCommented).to.equal(3)

    reset()
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-other-souls');
    expect(notifs.recent.notification.title).to.be.equal('normal post');
    expect(notifs.recent.notification.body).to.be.equal(`name 3: @name 1 this is a test comment on normal post !`);

    //user1 check get notification after c3, profile should not be null due of normal comment
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log('user1 notification after c3 res.body: ', res.body)
    expect(res.body.notifications[0].profile).to.not.equal(null);

    // user2 normal comment on normal post,
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q2Id,
        text: 'this is a test normal comment on friend post !',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);

    question2 = await Question.findOne({_id: q2Id})
    expect(question2.anonymousUsersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented).to.deep.equal(['3','2'])
    expect(question2.anonymousUsersThatCommented).to.deep.equal(['3', '0'])
    expect(question2.numUsersThatCommented).to.equal(4)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-matches');
    expect(notifs.recent.notification.title).to.be.equal('normal post');
    expect(notifs.recent.notification.body).to.be.equal(`name 2: @name 1 this is a test normal comment on friend post !`);

    // user0 comment anonynomously on normal post (friend post),
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q2Id,
        text: 'this is a test anonymous comment on friend post !',
        parentId: q2Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);

    question2 = await Question.findOne({_id: q2Id})
    expect(question2.anonymousUsersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented.length).to.equal(2)
    expect(question2.usersThatCommented).to.deep.equal(['3','2'])
    expect(question2.anonymousUsersThatCommented).to.deep.equal(['3', '0'])
    expect(question2.numUsersThatCommented).to.equal(4)

    reset()
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0)

     //user1 get feed should see 1 post that had only one friendsThatCommented
    res = await request(app)
      .get('/v1/question/feed')
      .query({ interestName: 'kpop' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log('user1 feed: ', res.body.questions)
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(1)
    expect(res.body.questions[0].friendsThatCommented[0]).to.deep.equal({_id: '2', firstName: 'name 2'});

    //check num comment on question when had normal comment and anonymous comment

    //user2 get allquestion should see 1 numComment
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    // expect(res.body.questions.length).to.equal(0)
    console.log('res body get questions: ', res.body.questions)
    expect(res.body.questions[0].numComments).to.equal(2);

    //user3 get allquestion should see 3 numComment on mormal post
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    // expect(res.body.questions.length).to.equal(0)
    console.log('res body get questions: ', res.body.questions)
    normalQuestion = res.body.questions.find((question) => question._id == q2Id)
    expect(normalQuestion.numComments).to.equal(5);


    // user0 reply normal comment anonymously
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q2Id,
        text: 'this is a anonymous reply comment on normal post !',
        parentId: c3Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);
    const c3R1Id = res.body._id;
    console.log('c3R1: ', res.body)
    resetTime = Date.now();

    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-other-souls');
    expect(notifs.recent.notification.title).to.be.equal('normal post');
    expect(notifs.recent.notification.body).to.be.equal(`Nickname 0: @name 3 this is a anonymous reply comment on normal post !`);

    // user1 reply normal comment, notification related comment should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q2Id,
        text: 'this is a reply comment on normal post !',
        parentId: c3Id,
      });
    expect(res.status).to.equal(200);
    const c3R2Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('new-comment-on-post-other-souls');

    //replied level 2 comment
    // user3 reply normal comment anonymously
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a normal reply comment on level 2 anonymous comment !',
        parentId: c3R1Id,
      });
    expect(res.status).to.equal(200);
    const c3R1R1Id = res.body._id;
    console.log('c3R1R1: ', res.body)
    expect(res.body.repliedToAnonymous).to.deep.equal(user0AnonymousProfile)
    resetTime = Date.now();

    // user1 get c3 comments, should not see any comment, in actual user even can't see q1
    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c3Id })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    console.log('c3Id replied: ', res.body.comments)
    expect(res.body.comments.length).to.equal(3);
    c3R1R1 = res.body.comments.find(comment => comment._id === c3R1R1Id)
    expect(c3R1R1.repliedToAnonymous).to.deep.equal(user0AnonymousProfile)


    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-other-souls');
    expect(notifs.recent.notification.title).to.be.equal('normal post');
    expect(notifs.recent.notification.body).to.be.equal(`name 3: @Nickname 0 this is a normal reply comment on level 2 anonymous comment !`);

    // user3 reply normal comment anonymously
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a normal reply comment on level 2 normal comment !',
        parentId: c3R2Id,
      });
    expect(res.status).to.equal(200);
    const c3R2R1Id = res.body._id;
    console.log('c3R2R1: ', res.body)
    expect(res.body.repliedTo._id).to.equal('1')
    resetTime = Date.now();

    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-comment-on-post-other-souls');
    expect(notifs.recent.notification.title).to.be.equal('normal post');
    expect(notifs.recent.notification.body).to.be.equal(`name 3: @name 1 this is a normal reply comment on level 2 normal comment !`);

    // user0 get q1 comments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);

    // user1 get q1 comments, should not see any comment, in actual user even can't see q1
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // user0 get q2 comments, should see 2 comments and first comment had 2 numComments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(4);
    console.log('res body get comments: ', res.body.comments)
    anonymousComment = res.body.comments.find((comment) => comment._id == c2Id)
    normalComment = res.body.comments.find((comment) => comment._id == c3Id)
    expect(normalComment.numComments).to.equal(4);
    expect(anonymousComment.postedAnonymouslyBy.anonymousProfileNickname).to.equal('Nickname 3')
    expect(normalComment.profilePreview._id).to.equal('3')
    expect(normalComment.profilePreview.firstName).to.equal('name 3')

    // user1 get q2 comments, should only see 1 comment and first comment had 1 numComments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log('q2 comments: ',res.body.comments)
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[1].numComments).to.equal(3);

    //user3 get user0 anonymous post
    res = await request(app)
      .get('/v1/user/anonymousQuestions')
      .query({ anonymousProfileNickname: 'Nickname 0' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1)
    console.log('res body get: ', res.body)

    //user3 get his anonymous comments
    res = await request(app)
      .get('/v1/user/anonymousComments')
      .query({ anonymousProfileNickname: 'Nickname 3' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2)
    console.log('res body get: ', res.body)

    //user3 get his normal comments
    res = await request(app)
      .get('/v1/user/comments')
      .query({ createdBy: '3' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    console.log('res body get: ', res.body)
    expect(res.body.comments.length).to.equal(3)


    //user1 get user3 anonymous comments
    res = await request(app)
      .get('/v1/user/anonymousComments')
      .query({ anonymousProfileNickname: 'Nickname 3' })
      .set('authorization', 1);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('get anonymous comments not allowed');

  })

  it('post friend comment Anonymously, isFriendComment should false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);


    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }

    const user0AnonymousProfile = {
      anonymousProfileNickname: `Nickname 0`,
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user0 send like to user3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user2 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'anonymous post',
      text: 'text1',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // console.log('res body post questions: ', res)
    expect(res.body.postedAnonymouslyBy).to.deep.equal(user0AnonymousProfile)


    //user0 numQuestionsPostedAnonymously should increase
    user0 = await User.findOne({_id: 0})
    expect(user0.metrics.numQuestionsPostedAnonymously).to.equal(1)

    const user3AnonymousProfile = {
      anonymousProfileNickname: 'Nickname 3',
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user3 comment anonynomously on anonynomous post, no notification related comment should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'this is a test anonynomous comment !',
        parentId: q1Id,
        postedAnonymously: true
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;
    console.log('anonynomous comment on friend post: ', res.body)

    expect(res.body.postedAnonymouslyBy).to.deep.equal(user3AnonymousProfile)

    // user0 get q1 comments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    console.log('q1 comments: ', res.body)

    anonymousComment = anonymousComment = res.body.comments.find((comment) => comment._id == c1Id)

    //anonymousComment isFriendComment should false for anonymous comment
    expect(anonymousComment.isFriendComment).to.equal(false)

    //user1 create Question normaly
    params = {
      interestName: 'kpop',
      title: 'normal post',
      text: 'text1',
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // user2 normal comment on normal post,
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q2Id,
        text: 'this is a test normal comment on friend post !',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c2Id = res.body._id;
    console.log('anonynomous comment on friend post: ', res.body)

    // user1 get q2 comments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    console.log('q2 comments: ', res.body)

    normalComment = res.body.comments.find((comment) => comment._id == c2Id)

    //normalComment isFriendComment should true for normal comment
    expect(normalComment.isFriendComment).to.equal(true)
  })

  it('comment Anonymously and mention other user should sent notification', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);


    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: `token${uid}`,
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }

    const user0AnonymousProfile = {
      anonymousProfileNickname: `Nickname 0`,
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user0 send like to user3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user2 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'anonymous post',
      text: 'text1',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // console.log('res body post questions: ', res)
    expect(res.body.postedAnonymouslyBy).to.deep.equal(user0AnonymousProfile)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs.numSent :', notifs.numSent)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.not.equal('mentioned-in-post');

    //user0 numQuestionsPostedAnonymously should increase
    user0 = await User.findOne({_id: 0})
    expect(user0.metrics.numQuestionsPostedAnonymously).to.equal(1)

    const user3AnonymousProfile = {
      anonymousProfileNickname: 'Nickname 3',
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    reset();

    // user3 comment mention user2, notification should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'this is a test mention user on anonynomous comment !',
        parentId: q1Id,
        postedAnonymously: true,
        mentionedUsersText: [
          {
            _id: '2',
            firstName: 'name 2',
          },
        ]
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;
    console.log('anonynomous comment on friend post: ', res.body)

    expect(res.body.postedAnonymouslyBy).to.deep.equal(user3AnonymousProfile)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs after c1:', notifs)
    // anonymous post notification should use soul name
    expect(notifs.recentArray[1].notification.title).to.equal('Nickname 3 mentioned you in a comment');
    expect(notifs.recentArray[1].notification.body).to.equal('this is a test mention user on anonynomous comment !');
    reset();

    // user3 comment normaly and mention user1, notification should not send
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'this is a test mention app 339 false user on normal comment !',
        parentId: q1Id,
        mentionedUsersText: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ]
      });
    expect(res.status).to.equal(200);
    const c2Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs after mention app 339 false :', notifs)

    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.not.equal('mentioned-in-post');

    params = {
      interestName: 'kpop',
      title: 'normal post',
      text: 'text normal',
      mentionedUsersText: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    q2Id = res.body._id;

    // user3 mention user1 on anonymous comment, normal post, notification should not sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a test mention user on anonynomous comment, normal post !',
        parentId: q2Id,
        postedAnonymously: true,
        mentionedUsersText: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ]
      });

    const c3Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs :', notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.not.equal('mentioned-in-post');
    reset()

    // user3 mention user1 on normal comment, normal post, notification should sent
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q2Id,
        text: 'this is a test mention user on normal comment, normal post !',
        parentId: q2Id,
        mentionedUsersText: [
          {
            _id: '1',
            firstName: 'name 1',
          },
        ]
      });

    const c4Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs :', notifs)
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('mentioned-in-post');
    reset()

    params = {
      interestName: 'kpop',
      title: 'normal post',
      text: 'by user 1 app 339 false',
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(200);
    q3Id = res.body._id;
    reset()

    res = await request(app)
    .post('/v1/comment')
    .set('authorization', 3)
    .send({
      questionId: q3Id,
      text: 'this is a test reply with anonynomous comment on normal post from app 339 false !',
      parentId: q3Id,
      postedAnonymously: true,
    });
    const c5Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs :', JSON.stringify(notifs, null, 2))
    expect(notifs.numSent).to.be.equal(1);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.be.equal('new-karma-level');
    reset()

    res = await request(app)
    .post('/v1/comment')
    .set('authorization', 3)
    .send({
      questionId: q3Id,
      text: 'this is a test reply anonynomous comment on normal post from app 339 false, anonymously !',
      parentId: c5Id,
      postedAnonymously: true,
    });
    expect(res.status).to.equal(200);
    const c5R1Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs last :', JSON.stringify(notifs, null, 2))
    expect(notifs.numSent).to.be.equal(0);
    reset()
  })

  it('anonymous post that had mention user should send notif', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);


    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }

    const user0AnonymousProfile = {
      anonymousProfileNickname: `Nickname 0`,
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user0 send like to user3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user2 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'anonymous post',
      text: 'text1',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '3',
          firstName: 'name 3',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // console.log('res body post questions: ', res)
    expect(res.body.postedAnonymouslyBy).to.deep.equal(user0AnonymousProfile)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs :', notifs)
    // anonymous post notification should use soul name
    expect(notifs.recent.notification.title).to.equal('Nickname 0 mentioned you in a post');
    expect(notifs.recent.notification.body).to.equal('anonymous post');


    reset();

    //user1 create Question normaly
    params = {
      interestName: 'kpop',
      title: 'normal post',
      text: 'text1',
      mentionedUsersTitle: [
        {
          _id: '0',
          firstName: 'name 0',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send(params);
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs :', notifs)
    //normal post should be normal
    expect(notifs.recentArray[1].notification.title).to.equal('name 1 mentioned you in a post');
    expect(notifs.recentArray[1].notification.body).to.equal('normal post');
    expect(notifs.recent.notification.title).to.equal('name 1 posted');
    expect(notifs.recent.notification.body).to.equal('normal post');

    reset();

  })

  it('anonymous post should not seen if traffic from web', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);


    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'male'});
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: uid });
      user.age = 31;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }

    const user0AnonymousProfile = {
      anonymousProfileNickname: `Nickname 0`,
      personality: { mbti: 'ESTJ', avatar: 'Executive' },
      enneagram: '1w2',
      horoscope: 'Capricorn',
      gender: 'male',
      age: 31
    }

    // user0 send like to user3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user2 send like to user1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    //user1 approve
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    //user0 create Question Anonymously mention user1
    let params = {
      interestName: 'kpop',
      title: 'anonymous post',
      text: 'text1',
      postedAnonymously: true,
      mentionedUsersTitle: [
        {
          _id: '1',
          firstName: 'name 1',
        },
      ]
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send(params);
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 3 os is ios, request feed
    res = await request(app)
      .get('/v1/question/feed')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
    expect(res.status).to.equal(200);
    console.log('user3 ios feed: ', res.body.questions)
    expect(res.body.questions.length).to.equal(1)

    // user 3 os is ios, but request feed from web
    res = await request(app)
      .get('/v1/question/feed')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from-web', true);
    expect(res.status).to.equal(200);
    console.log('user3 web feed: ', res.body.questions)
    expect(res.body.questions.length).to.equal(0)

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from-web', true);
    expect(res.status).to.equal(200);
    console.log('user3 web allQuestions: ', res.body.questions)
    expect(res.body.questions.length).to.equal(0)

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    console.log('user3 allQuestions: ', res.body.questions)
    expect(res.body.questions.length).to.equal(1)

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: q1Id })
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    console.log('user3 get question: ', res.body)
    expect(res.body.question._id).to.equal(q1Id)

    res = await request(app)
      .get('/v1/question')
      .query({ questionId: q1Id })
      .set('authorization', 3)
      .set('from-web', true);
    expect(res.status).to.equal(404);
    expect(res.text).to.equal('This post has been deleted.')

    res = await request(app)
      .get('/v1/question/feed')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from', 'web');
    expect(res.status).to.equal(200);
    console.log('user3 web feed: ', res.body.questions)
    expect(res.body.questions.length).to.equal(0)

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from', 'web');
    expect(res.status).to.equal(200);
    console.log('user3 web allQuestions: ', res.body.questions)
    expect(res.body.questions.length).to.equal(0)

    res = await request(app)
      .get('/v1/question/feed')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from', 'ios');
    expect(res.status).to.equal(200);
    console.log('user3 web feed: ', res.body.questions)
    expect(res.body.questions.length).to.equal(1)

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 3)
      .set('from', 'android');
    expect(res.status).to.equal(200);
    console.log('user3 web allQuestions: ', res.body.questions)
    expect(res.body.questions.length).to.equal(1)

  })

  it('reject profile comment anonymously', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    function getMockProfile(index, createdBy) {
      return {
        id: index,
        createdBy,
        horoscope:'Scorpio',
        mbti:'ENFP',
        enneagram:'2w1',
        name: `profile${index}`,
        description: `desc${index}`,
        subcategories: [1, 2],
        image: `database/profiles/${index}`,
        imageSource: `mockSource${index}`,
      };
    }

    for (let uid = 0; uid < 4; uid++){
      let attributes
      if(uid === 0 || uid === 3){
        // init user0 & user3 app 339 true
        attributes = { appVersion: '1.13.74', os: 'ios'}
      }else if(uid === 1){
        //init user1 with old version
        attributes = { appVersion: '1.13.73', os: 'ios'}
      }else if(uid === 2){
        //init user2 with os web
        attributes = { appVersion: '1.13.74', os: 'web'}
      }

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send(...[attributes]);
      expect(res.status).to.equal(200);
      expect(res.body.user.anonymousProfileNickname).to.be.undefined;

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      if(uid === 0 || uid === 3){
        res = await request(app)
          .put('/v1/user/anonymousProfileNickname')
          .set('authorization', uid)
          .send({ anonymousProfileNickname: `Nickname ${uid}`});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ'
          });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);

        await setEnneagram(uid,{enneagram:'1w2'});
      }
    }
    await (new Profile(getMockProfile(2, 1))).save();
    const dbProfileUser2 = await getWebDbProfile(2);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        parentId: dbProfileUser2._id,
        text: 'test comment profile',
        postedAnonymously: true
      });
      expect(res.status).to.equal(422);
      expect(res.text).to.equal('comment anonymously not allowed for profile comment');
  })
})
*/
