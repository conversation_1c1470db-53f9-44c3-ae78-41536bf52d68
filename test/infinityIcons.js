const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const moment = require('moment');
const { app, validImagePath } = require('./common');
const { validGoogleReceipt } = require('./iap');
const coinsConstants = require('../lib/coins-constants');
const User = require('../models/user');
const profilesLib = require('../lib/profiles-v3');
const basic = require('../lib/basic');
const UsersWhoLiked = require('../models/users-who-liked')
const TopPicksExclusionList = require('../models/top-picks-exclusion-list')

describe('showMyInfinityStatus', async () => {
    beforeEach(async () => {
        basic.assignConfig.restore();
        sinon.stub(basic, 'assignConfig').returns(true);

        const numUsers = 3;

        for (let uid = 0; uid < numUsers; uid++) {
            res = await request(app)
                .put('/v1/user/initApp')
                .set('authorization', uid)
                .send({ appVersion: '1.13.76' });
            expect(res.status).to.equal(200);
            expect(res.body.user.preferences.local).to.equal(true);
            expect(res.body.user.preferences.global).to.equal(true);

            res = await request(app)
                .patch('/v1/user/preferences')
                .set('authorization', uid)
                .send({
                gender: [
                    'female',
                ],
                personality: [
                    'ISTP',
                ],
                });
            expect(res.status).to.equal(200);
            res = await request(app)
                .put('/v1/user/gender')
                .set('authorization', uid)
                .send({ gender: 'female' });
            expect(res.status).to.equal(200);
            res = await request(app)
                .put('/v1/user/birthday')
                .set('authorization', uid)
                .send({
                year: 1990,
                month: 1,
                day: 1,
                });
            expect(res.status).to.equal(200);
            res = await request(app)
                .put('/v1/user/quizAnswers')
                .set('authorization', uid)
                .send({
                answers: {},
                });
            expect(res.status).to.equal(200);
            res = await request(app)
                .put('/v1/user/location')
                .set('authorization', uid)
                .send({
                longitude: 0,
                latitude: 51,
                });
            expect(res.status).to.equal(200);

            const user = await User.findOne({ _id: uid });
            user.createdAt = 0;
            user.viewableInDailyProfiles = true;
            await user.save();

            await User.ensureIndexes();
        }
    })


    it('premium user should able to set showMyInfinityStatus', async () => {

        //user0 will not able to set showMyInfinityStatus, before purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(403);

        //user0 purchase infinity
        res = await request(app)
            .put('/v1/user/purchasePremium')
            .set('authorization', 0)
            .send({
                receipt: validGoogleReceipt,
            });
        expect(res.status).to.equal(200);
        user = await User.findById('0');
        user.currentDayMetrics.topPicks = [];
        await user.save();

        //user0 will able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0)
            .send({ appVersion: '1.13.76' });
        expect(res.status).to.equal(200);
        console.log('res.body.user: ', res.body.user)
        expect(res.body.user.premium).to.equal(true);

        //user0 now able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(200);


        //user0 will able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0)
            .send({ appVersion: '1.13.76' });
        expect(res.status).to.equal(200);
        console.log('res.body.user: ', res.body.user)
        expect(res.body.user.premium).to.equal(true);
        expect(res.body.user.showMyInfinityStatus).to.equal(true);

        //user0 now able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: false,
            });
        expect(res.status).to.equal(200);

        res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0)
            .send({ appVersion: '1.13.76' });
        expect(res.status).to.equal(200);
        console.log('res.body.user: ', res.body.user)
        expect(res.body.user.premium).to.equal(true);
        expect(res.body.user.showMyInfinityStatus).to.equal(false);
    })

    it('user0 default showMyInfinityStatus is true but not premium yet, user1 and user2 should not see infinity icon', async () => {
        //user1 should not be able to see inifinty icon on user0 profile due of user0 is not premium
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 1);
        expect(res.status).to.equal(200);
        console.log('user 1 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            if(profile._id == 0){
                expect(profile.premium).to.be.undefined;
            }else{
                expect(profile.premium).to.be.undefined;
            }
        }

        //user1 see user0 profile
        res = await request(app)
            .get('/v1/user/profile')
            .set('authorization', 1)
            .query({ user: '0' });
        expect(res.status).to.equal(200);
        console.log('user 1 POV get user0 profile: ', res.body)
        expect(res.body.user.premium).to.be.undefined;

        //user2 should not be able to see inifinty icon on user0 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 2);
        expect(res.status).to.equal(200);
        console.log('user 2 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            expect(profile.premium).to.be.undefined;
        }

        //user2 see user0 profile
        res = await request(app)
            .get('/v1/user/profile')
            .set('authorization', 2)
            .query({ user: '0' });
        expect(res.status).to.equal(200);
        console.log('user 2 POV get user0 profile: ', res.body)
        expect(res.body.user.premium).to.be.undefined;
    })

    it('user0 set showMyInfinityStatus true, user1 and user2 should see it', async () => {
        //user0 will not able to set showMyInfinityStatus, before purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(403);

        //user0 purchase infinity
        res = await request(app)
            .put('/v1/user/purchasePremium')
            .set('authorization', 0)
            .send({
                receipt: validGoogleReceipt,
            });
        expect(res.status).to.equal(200);
        user = await User.findById('0');
        user.currentDayMetrics.topPicks = [];
        await user.save();

        //user0 now able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(200);


        // user0 not see inifinty icon on user1 and user2 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 0);
        expect(res.status).to.equal(200);
        console.log('user 0 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            expect(profile.premium).to.be.undefined;
        }


        //user1 should be able to see inifinty icon on user0 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 1);
        expect(res.status).to.equal(200);
        console.log('user 1 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            if(profile._id == 0){
                expect(profile.premium).to.equal(true);
            }else{
                expect(profile.premium).to.be.undefined;
            }
        }

        //user1 see user0 profile
        res = await request(app)
            .get('/v1/user/profile')
            .set('authorization', 1)
            .query({ user: '0' });
        expect(res.status).to.equal(200);
        console.log('user 1 POV get user0 profile: ', res.body)
        expect(res.body.user.premium).to.equal(true);
    })

    it('user0 set showMyInfinityStatus false, user1 and user2 should see it', async () => {
        //user0 will not able to set showMyInfinityStatus, before purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(403);

        //user0 purchase infinity
        res = await request(app)
            .put('/v1/user/purchasePremium')
            .set('authorization', 0)
            .send({
                receipt: validGoogleReceipt,
            });
        expect(res.status).to.equal(200);
        user = await User.findById('0');
        user.currentDayMetrics.topPicks = [];
        await user.save();

        //user0 now able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: false,
            });
        expect(res.status).to.equal(200);


        // user0 not see inifinty icon on user1 and user2 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 0);
        expect(res.status).to.equal(200);
        console.log('user 0 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            expect(profile.premium).to.be.undefined;
        }


        //user1 should not be able to see inifinty icon on user0 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 1);
        expect(res.status).to.equal(200);
        console.log('user 1 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            if(profile._id == 0){
                expect(profile.premium).to.be.undefined;
            }else{
                expect(profile.premium).to.be.undefined;
            }
        }

        //user1 see user0 profile
        res = await request(app)
            .get('/v1/user/profile')
            .set('authorization', 1)
            .query({ user: '0' });
        expect(res.status).to.equal(200);
        console.log('user 1 POV get user0 profile: ', res.body)
        expect(res.body.user.premium).to.be.undefined;

        //user2 should not be able to see inifinty icon on user0 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 2);
        expect(res.status).to.equal(200);
        console.log('user 2 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            expect(profile.premium).to.be.undefined;
        }

        //user2 see user0 profile
        res = await request(app)
            .get('/v1/user/profile')
            .set('authorization', 2)
            .query({ user: '0' });
        expect(res.status).to.equal(200);
        console.log('user 2 POV get user0 profile: ', res.body)
        expect(res.body.user.premium).to.be.undefined;
    })

    it('user0 set showMyInfinityStatus true, when user0 on top picks user1 and user2 should see it', async () => {
        //user0 will not able to set showMyInfinityStatus, before purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(403);

        //user0 purchase infinity
        res = await request(app)
            .put('/v1/user/purchasePremium')
            .set('authorization', 0)
            .send({
                receipt: validGoogleReceipt,
            });
        expect(res.status).to.equal(200);
        user = await User.findById('0');
        user.currentDayMetrics.topPicks = [];
        await user.save();

        for(let i = 1; i <= 2; i++){
            user = await User.findById(i);
            user.currentDayMetrics.topPicks = [0];
            await user.save();
        }

        //user0 now able to set showMyInfinityStatus, after purchase infinity
        res = await request(app)
            .put('/v1/user/showMyInfinityStatus')
            .set('authorization', 0)
            .send({
                showMyInfinityStatus: true,
            });
        expect(res.status).to.equal(200);


        // user0 not see inifinty icon on user1 and user2 profile
        res = await request(app)
            .get('/v1/user/dailyProfiles')
            .set('authorization', 0);
        expect(res.status).to.equal(200);
        console.log('user 0 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(2);
        for(let profile of res.body.profiles){
            expect(profile.premium).to.be.undefined;
        }


        //user1 should be able to see inifinty icon on user0 profile
        res = await request(app)
            .get('/v1/user/topPicks')
            .set('authorization', 1);
        expect(res.status).to.equal(200);
        console.log('user 1 POV res.body.profiles: ', res.body.profiles)
        expect(res.body.profiles.length).to.equal(1);
        expect(res.body.profiles[0]._id).to.equal('0');
        expect(res.body.profiles[0].premium).to.equal(true);
    })
})
