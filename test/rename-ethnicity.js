const { expect } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const User = require('../models/user');
const { renameEthnicity } = require('../lib/rename-ethnicity');

const oldName = 'Chinese';
const newName = 'Han Chinese';

describe('rename ethnicities', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.24' })
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql();
  });

  it('old name not found', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Korean']);

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Korean']);
  });

  it('old name in ethnicities field', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', oldName, 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', oldName, 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', newName, 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(null);
  });

  it('old name in preferences.ethnicities field', async () => {
    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', oldName, 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', oldName, 'Korean']);

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', newName, 'Korean']);
  });

  it('substring should not match', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Chinese Tatar', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Chinese Tatar', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Chinese Tatar', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();
  });

  it('rename Berber to Amazigh', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
  });

  it('remove duplicates after renaming', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean', 'Amazigh'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean', 'Amazigh'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
  });

  it('rename Anglo-Irish to Irish', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean', 'Irish'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean', 'Irish'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Irish', 'Korean' ]);

    let user = await User.findOne({ _id: 0 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean', 'Irish'];
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean', 'Irish'];
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Anglo-Irish', 'Korean', 'Irish']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Anglo-Irish', 'Korean', 'Irish']);

    await renameEthnicity('Anglo-Irish', 'Irish');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Korean', 'Irish']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Korean', 'Irish']);

    // without Irish already selected
    res = await request(app)
      .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Irish', 'Korean' ]);

    user = await User.findOne({ _id: 0 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Anglo-Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Anglo-Irish', 'Korean' ]);

    await renameEthnicity('Anglo-Irish', 'Irish');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
  });

  it('handle null and undefined ethnicity fields', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.24' })
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql();
    // Test case 1: User with ethnicities but no preferences.ethnicities
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean'],
      });
    expect(res.status).to.equal(200);

    // Don't set preferences.ethnicities (should remain null/undefined)
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();

    user = await User.findOne({ _id: 0 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    user = await User.findOne({ _id: 1 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    await renameEthnicity('Anglo-Irish', 'Irish');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(null);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);

    // Test case 2: User with preferences.ethnicities but undefined ethnicities
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Anglo-Irish', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);

    user = await User.findOne({ _id: 0 });
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    user = await User.findOne({ _id: 1 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    await renameEthnicity('Anglo-Irish', 'Irish');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);

    // Test case 3: User with both fields undefined (no Anglo-Irish to rename)
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql();

    user = await User.findOne({ _id: 1 });
    user.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    user.preferences.ethnicities = ['Asian', 'Anglo-Irish', 'Korean'];
    await user.save();

    await renameEthnicity('Anglo-Irish', 'Irish');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Irish', 'Korean']);

  });

});

