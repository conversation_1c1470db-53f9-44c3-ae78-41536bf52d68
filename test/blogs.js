const { expect } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const Blogs = require('../models/blogs');

describe('blogs user poll', async () => {
  it('user should be able to poll in the blogs', async () => {
    
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 2)
      .send({ mbti: 'ISTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 3)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 4)
      .send({ mbti: 'ENFJ' });
    expect(res.status).to.equal(200);

    await Blogs.insertMany([
      { url: '/blogs/purposeful-dating' },
      { url: '/resources/blogs/purposeful-dating' },
    ]);

    const blogsUrl = await Blogs.distinct('url', {});

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 1)
      .send({ option: 1 });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 1)
      .send({ blogUrl: blogsUrl[0], option: 'option' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/blogs/user/poll')
      .set('authorization', 1)
      .query({ blogUrl: blogsUrl[0] });
    expect(res.status).to.equal(200);
    expect(res.body.poll).to.eql({});

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 1)
      .send({ blogUrl: blogsUrl[0], option: 1 });
    expect(res.status).to.equal(200);

    let blog = await Blogs.findOne({ url: blogsUrl[0] })
    expect(blog.pollData).to.eql({ '1': { ESTJ: 1 } });

    res = await request(app)
      .get('/blogs/user/poll')
      .set('authorization', 1)
      .query({ blogUrl: blogsUrl[0] });
    expect(res.status).to.equal(200);
    expect(res.body.poll).to.eql({
      blogUrl: '/blogs/purposeful-dating',
      mbti: 'ESTJ',
      option: 1
      });

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 1)
      .send({ blogUrl: blogsUrl[0], option: 1 });
    expect(res.status).to.equal(403);

    blog = await Blogs.findOne({ url: blogsUrl[0] })
    expect(blog.pollData).to.eql({ '1': { ESTJ: 1 } });

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 2)
      .send({ blogUrl: blogsUrl[0], option: 1 });
    expect(res.status).to.equal(200);

    blog = await Blogs.findOne({ url: blogsUrl[0] })
    expect(blog.pollData).to.eql({ '1': { ESTJ: 1, ISTJ: 1 } });

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 3)
      .send({ blogUrl: blogsUrl[0], option: 1 });
    expect(res.status).to.equal(200);

    blog = await Blogs.findOne({ url: blogsUrl[0] })
    expect(blog.pollData).to.eql({ '1': { ESTJ: 2, ISTJ: 1 } });

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 4)
      .send({ blogUrl: blogsUrl[0], option: 0 });
    expect(res.status).to.equal(200);

    blog = await Blogs.findOne({ url: blogsUrl[0] })
    expect(blog.pollData).to.eql({
      '0': { ENFJ: 1 },
      '1': { ESTJ: 2, ISTJ: 1 },
    });

    res = await request(app)
      .patch('/blogs/poll')
      .set('authorization', 4)
      .send({ blogUrl: blogsUrl[1], option: 0 });
    expect(res.status).to.equal(200);

    blog = await Blogs.findOne({ url: blogsUrl[1] })
    expect(blog.pollData).to.eql({
      '0': { ENFJ: 1 },
    });

  });
});

