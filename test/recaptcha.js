const { expect } = require('chai');
const sinon = require('sinon');
const axios = require('axios');
const request = require('supertest');
const { app, mongoose, validImagePath, validYotiPayload } = require('./common');
const { stubAuthEmailDomain } = require('./stub');
const recaptchaLog = require('../models/recaptchaLog');
const constants = require('../lib/constants');
const User = require('../models/user');

describe('check recaptcha verify', () => {
  afterEach(() => {
    sinon.restore();
    delete process.env.RECAPTCHA_SECRET_KEY
  });

  it('should verify recaptcha and log successfully for initApp', async () => {

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ captchaToken: 'test' });
    expect(res.status).to.equal(200);

    let recaptchaLogs = await recaptchaLog.find({ });
    expect(recaptchaLogs.length).to.equal(0);

    process.env.RECAPTCHA_SECRET_KEY = 'test_key';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ captchaToken: 'test' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({});
    expect(recaptchaLogs.length).to.equal(1);

    let recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
    expect(recaptchaLogDoc.success).to.equal(false);
    expect(recaptchaLogDoc.user).to.equal('0');
    expect(recaptchaLogDoc.errorCodes).to.eql(['invalid-input-response']);

    const stub = sinon.stub(axios, 'post').resolves({
      data: {
        success: true,
        score: 0.9,
        action: 'initApp',
        challenge_ts: new Date().toISOString(),
        hostname: 'localhost',
        error_codes: null,
      },
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ captchaToken: 'test' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({ success: true });
    expect(recaptchaLogs.length).to.equal(1);

    expect(stub.calledOnce).to.be.true;
    expect(stub.args[0][0]).to.equal('https://www.google.com/recaptcha/api/siteverify');
    expect(stub.args[0][1]).to.deep.include({
      secret: process.env.RECAPTCHA_SECRET_KEY,
      response: 'test',
    });

    recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
    expect(recaptchaLogDoc.score).to.equal(0.9);
    expect(recaptchaLogDoc.user).to.equal('1');
    expect(recaptchaLogDoc.action).to.equal('initApp');
    expect(recaptchaLogDoc.hostname).to.equal('localhost');
    expect(recaptchaLogDoc.errorCodes).to.eql([]);

  });

  it('should verify recaptcha and log successfully for isAuthAllowed', async () => {

      let res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ captchaToken: 'test' });
      expect(res.status).to.equal(200);

      let recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(0);

      process.env.RECAPTCHA_SECRET_KEY = 'test_key';

      res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ captchaToken: 'test' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(1);

      let recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
      expect(recaptchaLogDoc.success).to.equal(false);
      expect(recaptchaLogDoc.user).to.equal(undefined);
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.errorCodes).to.eql(['invalid-input-response']);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 100)
        .send({ email: '<EMAIL>' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(1);

      recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
      expect(recaptchaLogDoc.success).to.equal(false);
      expect(recaptchaLogDoc.user).to.equal('100');
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.errorCodes).to.eql(['invalid-input-response']);

      const stub = sinon.stub(axios, 'post').resolves({
        data: {
          success: true,
          score: 0.9,
          action: 'isAuthAllowed',
          challenge_ts: new Date().toISOString(),
          hostname: 'localhost',
          error_codes: null,
        },
      });

      res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ captchaToken: 'test' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(2);
      recaptchaLogs = await recaptchaLog.find({ success: true });
      expect(recaptchaLogs.length).to.equal(1);

      expect(stub.calledOnce).to.be.true;
      expect(stub.args[0][0]).to.equal('https://www.google.com/recaptcha/api/siteverify');
      expect(stub.args[0][1]).to.deep.include({
        secret: process.env.RECAPTCHA_SECRET_KEY,
        response: 'test',
      });

      recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
      expect(recaptchaLogDoc.score).to.equal(0.9);
      expect(recaptchaLogDoc.user).to.equal(undefined);
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.action).to.equal('isAuthAllowed');
      expect(recaptchaLogDoc.hostname).to.equal('localhost');
      expect(recaptchaLogDoc.errorCodes).to.eql([]);

      res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 101)
      .send({ email: '<EMAIL>' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({ success: true });
    expect(recaptchaLogs.length).to.equal(1);

    recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
    expect(recaptchaLogDoc.success).to.equal(true);
    expect(recaptchaLogDoc.user).to.equal('101');
    expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
    expect(recaptchaLogDoc.errorCodes).to.eql([]);
    });
});

describe('check turnstile verify', () => {
  beforeEach(() => {
    constants.requireManualVerificationForWeb.restore();
    sinon.stub(constants, 'requireManualVerificationForWeb').returns(true);
    stubAuthEmailDomain('gmail.com');
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.TURNSTILE_SECRET_KEY
  });

  it('should verify turnstile and log successfully for initApp', async () => {

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ turnstileToken: 'testToken' });
    expect(res.status).to.equal(200);

    let recaptchaLogs = await recaptchaLog.find({ });
    expect(recaptchaLogs.length).to.equal(0);

    process.env.TURNSTILE_SECRET_KEY = 'test_key';

    // user 0: turnstile fail
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ turnstileToken: 'testToken' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({});
    expect(recaptchaLogs.length).to.equal(1);

    let recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
    expect(recaptchaLogDoc.success).to.equal(false);
    expect(recaptchaLogDoc.user).to.equal('0');
    expect(recaptchaLogDoc.errorCodes).to.eql(['ERR_BAD_REQUEST']);
    expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
    expect(recaptchaLogDoc.turnstileToken).to.equal('testToken');
    expect(recaptchaLogDoc.path).to.equal('/v1/user/initApp');

    /*
    // verification should be pending
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 0)
      .send(validYotiPayload);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('pending');
    expect(res.body.rejectionReason).to.equal(undefined);

    user = await User.findById('0');
    expect(user.verificationHistory.length).to.equal(1);
    expect(user.verificationHistory[0].status).to.equal('pending');
    expect(user.verificationHistory[0].by).to.equal();
    expect(user.verificationHistory[0].reason).to.equal('failed turnstile');
    */

    // user 1: turnstile pass
    const stub = sinon.stub(axios, 'post').resolves({
      data: {
        success: true,
        action: 'initApp',
        challenge_ts: new Date().toISOString(),
        hostname: 'localhost',
        error_codes: null,
      },
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ turnstileToken: 'testToken' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({ success: true });
    expect(recaptchaLogs.length).to.equal(1);

    expect(stub.calledOnce).to.be.true;
    expect(stub.args[0][0]).to.equal('https://challenges.cloudflare.com/turnstile/v0/siteverify');

    recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
    expect(recaptchaLogDoc.user).to.equal('1');
    expect(recaptchaLogDoc.action).to.equal('initApp');
    expect(recaptchaLogDoc.hostname).to.equal('localhost');
    expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
    expect(recaptchaLogDoc.errorCodes).to.eql([]);
    expect(recaptchaLogDoc.turnstileToken).to.equal(null);
    expect(recaptchaLogDoc.path).to.equal('/v1/user/initApp');

    /*
    // verification should be approved
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 1)
      .send(validYotiPayload);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal(undefined);
    */
  });

  it('should verify turnstile and log successfully for isAuthAllowed', async () => {

      let res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ turnstileToken: 'testToken' });
      expect(res.status).to.equal(200);

      let recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(0);

      process.env.TURNSTILE_SECRET_KEY = 'test_key';

      // user 100: turnstile fail
      res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ turnstileToken: 'testToken' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(1);

      let recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
      expect(recaptchaLogDoc.success).to.equal(false);
      expect(recaptchaLogDoc.user).to.equal(undefined);
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.errorCodes).to.eql(['ERR_BAD_REQUEST']);
      expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
      expect(recaptchaLogDoc.turnstileToken).to.equal('testToken');
      expect(recaptchaLogDoc.path).to.equal('/web/isAuthAllowed');

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 100)
        .send({ email: '<EMAIL>' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(1);

      recaptchaLogDoc = await recaptchaLog.findOne({ success: false });
      expect(recaptchaLogDoc.success).to.equal(false);
      expect(recaptchaLogDoc.user).to.equal('100');
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.errorCodes).to.eql(['ERR_BAD_REQUEST']);
      expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
      expect(recaptchaLogDoc.turnstileToken).to.equal('testToken');
      expect(recaptchaLogDoc.path).to.equal('/web/isAuthAllowed');

      // verification should be pending
      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', 100)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', 100)
        .send(validYotiPayload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('pending');
      expect(res.body.rejectionReason).to.equal(undefined);

      // user 101: turnstile pass
      const stub = sinon.stub(axios, 'post').resolves({
        data: {
          success: true,
          action: 'isAuthAllowed',
          challenge_ts: new Date().toISOString(),
          hostname: 'localhost',
          error_codes: null,
        },
      });

      res = await request(app)
        .put('/web/isAuthAllowed')
        .send({ deviceId: '0', email: '<EMAIL>' })
        .send({ turnstileToken: 'testToken' });
      expect(res.status).to.equal(200);

      recaptchaLogs = await recaptchaLog.find({});
      expect(recaptchaLogs.length).to.equal(2);
      recaptchaLogs = await recaptchaLog.find({ success: true });
      expect(recaptchaLogs.length).to.equal(1);

      expect(stub.calledOnce).to.be.true;
      expect(stub.args[0][0]).to.equal('https://challenges.cloudflare.com/turnstile/v0/siteverify');

      recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
      expect(recaptchaLogDoc.user).to.equal(undefined);
      expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
      expect(recaptchaLogDoc.action).to.equal('isAuthAllowed');
      expect(recaptchaLogDoc.hostname).to.equal('localhost');
      expect(recaptchaLogDoc.errorCodes).to.eql([]);
      expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
      expect(recaptchaLogDoc.turnstileToken).to.equal(null);
      expect(recaptchaLogDoc.path).to.equal('/web/isAuthAllowed');

      res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 101)
      .send({ email: '<EMAIL>' });
    expect(res.status).to.equal(200);

    recaptchaLogs = await recaptchaLog.find({ success: true });
    expect(recaptchaLogs.length).to.equal(1);

    recaptchaLogDoc = await recaptchaLog.findOne({ success: true });
    expect(recaptchaLogDoc.success).to.equal(true);
    expect(recaptchaLogDoc.user).to.equal('101');
    expect(recaptchaLogDoc.email).to.equal('<EMAIL>');
    expect(recaptchaLogDoc.errorCodes).to.eql([]);
    expect(recaptchaLogDoc.logFrom).to.equal('turnstile');
    expect(recaptchaLogDoc.turnstileToken).to.equal(null);
    expect(recaptchaLogDoc.path).to.equal('/web/isAuthAllowed');

    /*
    // verification should be approved
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 101)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 101)
      .send(validYotiPayload);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal(undefined);
    */

    });
});
