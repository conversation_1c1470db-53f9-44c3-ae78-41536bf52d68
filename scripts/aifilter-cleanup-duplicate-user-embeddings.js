/*
Required Env:
- MONGODB_URI_RECORDS
- MONGODB_URI
- REPLICATE_KEY
- AWS_KEY
- AWS_SECRET
- AWS_S3_REGION
- IMAGE_DOMAIN
- REPLICATE_MODEL_TYPE (custom/public) default custom
*/

require('log-timestamp');
const mongoose = require('mongoose');
const User = require('../models/user');
const UserEmbedding = require('../models/user-embeddings');
const { generateEmbedding, mergeEmbeddings } = require('../lib/aifilter/helper');
const { IMAGE_DOMAIN } = require('../lib/constants');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find users with multiple embedding records
    const duplicateRecords = await UserEmbedding.aggregate([
      { $sort: { user: 1 } },
      {
        $group: {
          _id: '$user',
          count: { $sum: 1 },
        },
      },
      { $match: { count: { $gt: 1 } } },
      { $project: { _id: 0, user: '$_id' } },
    ]);

    const duplicateUsers = duplicateRecords.map((r) => r.user);
    if (!duplicateUsers.length) {
      console.log('No duplicate user embedding records found.');
      return;
    }

    // Pull latest user profile data
    const latestUserDataMap = new Map();
    const latestData = await User.find({ _id: { $in: duplicateUsers } })
      .select('_id pictures latitude longitude countryCode')
      .lean();

    latestData.forEach((data) => latestUserDataMap.set(data._id.toString(), data));

    // Merge each duplicate users embeddings
    for (const userId of duplicateUsers) {
      const userEmbeddings = await UserEmbedding.find({ user: userId }).sort({ createdAt: -1 });
      if (userEmbeddings.length <= 1) continue;

      const userProfile = latestUserDataMap.get(userId.toString());
      if (!userProfile) {
        console.log(`No profile found for user ${userId}, deleting embed records.`);
        await UserEmbedding.deleteMany({ user: userId });
        continue;
      }

      let latestPictures = userProfile.pictures?.filter((pic) => ['.jpg', '.jpeg', '.png'].some((ext) => pic.toLowerCase().endsWith(ext))) || [];

      // Prepare a single array of all picture embeddings
      let allEmbeddings = userEmbeddings.flatMap((record) => record.pictureEmbeddings.map((pic) => ({ key: pic.key, embedding: pic.embedding })));

      const pictureSet = new Set(latestPictures);

      // Filter any embeddings that are not in the latest profile pictures
      allEmbeddings = allEmbeddings.filter((pic) => pictureSet.has(pic.key));

      // Check for any pictures that are not in the existing embeddings
      const missingKeys = latestPictures.filter((pic) => !allEmbeddings.some((emb) => emb.key === pic));

      if (missingKeys.length) {
        const newEmbeddings = await Promise.all(
          missingKeys.map(async (imageKey) => {
            const embedding = await generateEmbedding(`${IMAGE_DOMAIN}${imageKey}`);
            return embedding ? { key: imageKey, embedding } : null;
          }),
        );

        allEmbeddings.push(...newEmbeddings.filter(Boolean));
      }

      const embeddings = allEmbeddings.map((e) => e.embedding);
      if (!embeddings.length) {
        console.log(`No embeddings generated for user ${userId}, skipping.`);
        continue;
      }

      const mergedEmbedding = embeddings.length === 1 ? embeddings[0] : await mergeEmbeddings(embeddings);

      // Overwrite with new single record
      await UserEmbedding.deleteMany({ user: userId });

      await UserEmbedding.create({
        user: userId,
        merged_embedding: mergedEmbedding,
        pictureEmbeddings: allEmbeddings,
        latitude: userProfile.latitude,
        longitude: userProfile.longitude,
        countryCode: userProfile.countryCode,
        source: 'backfill_v2',
      });

      console.log(`Merged and updated embeddings for user ${userId}`);
    }
  } catch (e) {
    console.log('Error in merging user embeddings:', e);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
