const mongoose = require("mongoose");
const Profile = require("../models/profile");

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

async function updateProfiles() {
  try {
    const result = await Profile.updateMany(
      {
        "subcategories": { $eq: 4155, $ne: 10227 }
      },
      {
        $addToSet: { "subcategories": 10227 }
      }
    )
    console.log(`Updated profiles subcategory::`,result);
  } catch (error) {
    console.error("Error updating profiles:", error);
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    console.log("Backfilling Profiles...");
    await updateProfiles();
    console.log("Profiles Backfill Complete");

    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  } catch (error) {
    console.error("Error during backfill:", error);
  }
})();
