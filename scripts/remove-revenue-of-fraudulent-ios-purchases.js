const mongoose = require('mongoose');
const PurchaseReceipt = require('../models/purchase-receipt');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const res = await PurchaseReceipt.updateMany(
    {
      purchaseDate: { $gt: new Date('2025-02-01') },
      service: 'apple',
      'fullReceipt.bundleId': 'com.mutyrgef.faxjp',
    },
    {
      revenue: 0,
      isFraudulent: true,
    },
  );
  console.log(res);

  await mongoose.disconnect();
})();
