const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { parse } = require('csv-parse/sync');
const Profile = require('../models/profile');

const MONGO_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 10000;

async function updateDuplicatedField() {
  try {
    await mongoose.connect(MONGO_URI);
    console.log('Connected to MongoDB');

    const csvPath = path.join(__dirname, 'duplicate-profiles-data.csv');
    const fileContent = fs.readFileSync(csvPath, 'utf8');

    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
    });

    const profileIds = records.flatMap(record =>
      record['Profile IDs']
        ? record['Profile IDs']
            .split(',')
            .slice(1) // Skip the first ID so at least 1 profile will be there to get linked with each slug
            .map(id => id.trim())
            .filter(Boolean)
        : []
    );
    

    console.log(`Found ${profileIds.length} profile IDs to update.`);

    let totalUpdated = 0;
    for (let i = 0; i < profileIds.length; i += BATCH_SIZE) {
      const batch = profileIds.slice(i, i + BATCH_SIZE);
      const result = await Profile.updateMany(
        { id: { $in: batch } },
        { $set: { duplicated: true } }
      );
      totalUpdated += result.modifiedCount || 0;
      console.log(`Updated batch ${i / BATCH_SIZE + 1}, Modified: ${result.modifiedCount}`);
    }

    console.log(`All done! Total profiles updated: ${totalUpdated}`);
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (err) {
    console.error('Error during update:', err);
    process.exit(1);
  }
}

updateDuplicatedField().catch(err => {
  console.error(err);
  process.exit(1);
});
