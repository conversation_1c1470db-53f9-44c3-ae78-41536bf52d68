const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const { setDefaultNewNotifications } = require('./set-default-new-notifications')

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(
    MONGODB_URI,
  );
  console.log('Connected to database');

  await setDefaultNewNotifications()

  await mongoose.disconnect();
})();
