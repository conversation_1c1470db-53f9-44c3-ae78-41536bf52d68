const mongoose = require('mongoose');
const User = require('../models/user');
const ImageModeration = require('../models/image-moderation');
const { findSpamInImage } = require('../lib/ocr-moderation');
const reportLib = require('../lib/report');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');

  const users = await User.find({
    createdAt: { $gt: new Date('2024-12-25') },
    os: 'web',
    shadowBanned: false,
    'verification.status': 'unverified',
    'pictures.0': { $exists: true },
  });
  console.log(`Processing ${users.length} users`);

  let numProcessed = 0;
  let numBanned = 0;
  for (const user of users) {
    console.log(`Processing user: ${user._id}`);
    const imageModerations = await ImageModeration.find({ key: { $in: user.pictures } });
    console.log(`Found ${imageModerations.length} image moderation results`);
    const imagesWithText = imageModerations.filter((x) => x.moderationLabels.some((label) => ['text', 'yes_overlay_text'].includes(label.Name))).map((x) => x.key);
    console.log(`Found ${imagesWithText.length} images with text`);
    for (const key of imagesWithText) {
      const spamText = await findSpamInImage(key, user._id);
      if (spamText) {
        console.log(`Found spam: ${key}, ${spamText}`);
        await reportLib.shadowBan(user, null, 'unverified with infringing text (found by OCR moderation)', spamText);
        numBanned++;
        break;
      }
    }
    numProcessed++;
    if (numProcessed % 100 == 0) {
      console.log(`numProcessed: ${numProcessed}, numBanned: ${numBanned}`);
    }
  }

  console.log(`numProcessed: ${numProcessed}, numBanned: ${numBanned}`);
  console.log('Done');
  await mongoose.disconnect();
})();
