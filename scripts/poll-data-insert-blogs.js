const fs = require('fs');
const mongoose = require('mongoose');
const { parse } = require('csv-parse/sync');
const Blog = require('../models/blogs');
const FILE_PATH = `${__dirname}/poll-data.csv`;

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

async function processCSV(filePath) {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const records = parse(fileContent, { columns: true, skip_empty_lines: true });
      const pollDataMap = {}; 
      records.forEach((row) => {
          let url = row['URL'].replace('https://boo.world', '').trim();
          if (!url.startsWith('/')) url = `/${url}`; // Ensure all URLs start with '/'
          const type = row['Type'];
          const option1 = parseInt(row['Option 1'], 10);
          const option2 = parseInt(row['Option 2'], 10);
          if (!pollDataMap[url]) pollDataMap[url] = { 0: {}, 1: {} };
          pollDataMap[url][0][type] = option1;
          pollDataMap[url][1][type] = option2;
      });
      return pollDataMap;
  }

(async () => {
      try {
            await mongoose.connect(MONGODB_URI);
            const pollDataMap = await processCSV(FILE_PATH);
            for (const urlWithSlash of Object.keys(pollDataMap)) {
                let urlWithoutSlash = urlWithSlash.startsWith('/') ? urlWithSlash.substring(1) : urlWithSlash;
                await Blog.updateOne(
                    { $or: [{ url: urlWithSlash }, { url: urlWithoutSlash }] }, // Match both formats
                    { $set: { pollData: pollDataMap[urlWithSlash] } },
                );

                console.log(`Updated: ${urlWithSlash} (or ${urlWithoutSlash})`);
            }
    
            console.log('Database update completed.');
        } catch (error) {
            console.error('Error updating database:', error);
        } finally {
            mongoose.connection.close();
        }
})();
