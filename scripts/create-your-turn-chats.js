const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const Chat = require('../models/chat');
const User = require('../models/user');
const Message = require('../models/message');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

const userId = 'N6Ej8yvHPcQZShYIJVvrN7NWx693';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const partnerIds = await User.find(
    {
      _id: { $ne: userId },
      banned: { $ne: true },
    },
    '_id',
    { limit: 55 },
  );
  for (partnerId of partnerIds) {
    // create chat if it does not exist
    let chat = await Chat.findOne({
      users: { $all: [userId, partnerId] },
    });
    if (!chat) {
      console.log(`Creating chat with ${partnerId}`);
      chat = new Chat({
        users: [userId, partnerId],
        pendingUser: null,
        pending: false,
        lastMessageTime: Date.now(),
      });
      await chat.save();
    }
    let message = new Message({
      chat: chat._id,
      sender: partnerId,
      text: 'test',
    });
    await message.save();

    chat.lastMessage = message._id;
    chat.numMessages += 1;
    chat.messaged = true,
    await chat.save();
  }

  await mongoose.disconnect();
})();
