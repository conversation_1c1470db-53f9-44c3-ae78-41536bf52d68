/*
ENV REQUIRED:
MONGODB_URI
NODE_ENV
AWS_REKOGNITION_KEY
AWS_REKOGNITION_SECRET
AWS_S3_REGION
AWS_S3_BUCKET
AWS_CLOUDWATCH_KEY
AWS_CLOUDWATCH_SECRET
*/

const mongoose = require('mongoose');
const BannedUser = require('../models/banned-user');
const { rekognition, addBannedFaceToIndex, UPDATED_BANNED_USERS_COLL_ID } = require('../lib/rekognition');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 5000;

const processBannedUser = async (bannedUser) => {
  try {
    const { userData } = bannedUser;
    if (!userData) {
      console.log(`No userData found for banned user. Record ID: ${bannedUser._id}`);
      return;
    }

    const faceIds = [];
    const addToBannedCollection = async (key) => {
      const faceId = await addBannedFaceToIndex(key, userData._id, UPDATED_BANNED_USERS_COLL_ID);
      if (faceId) faceIds.push(faceId);
    };

    const tasks = [];
    if (['verified', 'reverifying'].includes(userData.verification?.status)) {
      const verificationPictures = [
        userData.verification?.pictures?.slice(-1)?.[0], // Last verification picture
        userData.livenessVerification?.frames?.[0]?.key, // First liveness frame
      ].filter(Boolean);

      tasks.push(...verificationPictures.map(addToBannedCollection));
    }

    await Promise.all(tasks);

    if (faceIds.length > 0) {
      await BannedUser.findOneAndUpdate({ _id: bannedUser._id }, { $set: { faceIds } }, { upsert: false });
    }
  } catch (error) {
    console.log(`Error processing user ${bannedUser.userData?._id}:`, error.message);
  }
};

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');

  // Check if collection already exists, if not create it
  const data = await rekognition.listCollections().promise();
  if (data?.CollectionIds?.includes(UPDATED_BANNED_USERS_COLL_ID)) {
    console.log(`Collection ${UPDATED_BANNED_USERS_COLL_ID} already exists`);
  } else {
    const { StatusCode } = await rekognition.createCollection({ CollectionId: UPDATED_BANNED_USERS_COLL_ID }).promise();
    if (StatusCode === 200) {
      console.log(`Collection ${UPDATED_BANNED_USERS_COLL_ID} created successfully`);
    }
  }

  let hasMoreData = true;
  let lastId = null;
  let totalProcessed = 0;

  while (hasMoreData) {
    try {
      const filter = {
        ...lastId ? { _id: { $gt: lastId } } : {},
        'userData.verification.status': { $in: ['verified', 'reverifying'] },
      };
      // eslint-disable-next-line newline-per-chained-call
      const batch = await BannedUser.find(filter).sort({ _id: 1 }).limit(BATCH_SIZE).select({
        _id: 1,
        'userData._id': 1,
        'userData.verification': 1,
        'userData.pictures': 1,
        'userData.livenessVerification': 1,
        'userData.bannedReason': 1,
      });

      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      await Promise.all(batch.map(processBannedUser));
      totalProcessed += batch.length;
      lastId = batch[batch.length - 1]._id;

      console.log(`Total processed ${totalProcessed} banned users`);
    } catch (batchError) {
      console.log(`Error processing batch last id ${lastId}:`, batchError.message);
    }
  }

  console.log('Finished processing all banned users');

  await mongoose.disconnect();
  console.log('Disconnected from MongoDB');
  process.exit(0);
})();
