const mongoose = require('mongoose');
const User = require('../models/user');
const Interest = require('../models/interest');
const InterestPoint = require('../models/interest-point');
const { updateInterestRanks } = require('../lib/social');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(
    MONGODB_URI,
  );
  console.log('Connected to database');

  const interest = 'gaming';
  const language = 'en';
  const numUsers = 250;
  const users = await User.find().limit(numUsers);
  for (let i = 0; i < numUsers; i++) {
    const user = users[i];
    if (!user.interestNames) {
      user.interestNames = [];
    }
    if (user.interestNames.includes(interest)) {
      continue;
    }
    user.interestNames.push(interest);
    if (!user.metrics.lastSeen) {
      user.metrics.lastSeen = new Date();
    }
    await user.save();

    await InterestPoint.updateOne(
      { user: user._id, interest, language },
      { $set: { points: i } },
      { upsert: true },
    );
  }

  await updateInterestRanks(interest, language);

  {
    const users = await User.find({ interestNames: interest });
    for (const user of users) {
      if (!user.metrics.lastSeen) {
        user.metrics.lastSeen = new Date();
        await user.save();
      }
    }
  }

  console.log('Done');
  await mongoose.disconnect();
})();
