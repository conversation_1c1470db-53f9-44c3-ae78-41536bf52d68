const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');
const { bannedEmailDomains } = require('../lib/report-constants');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    createdAt: { $gt: new Date('2025-02-14') },
    emailDomain: { $in: bannedEmailDomains },
    shadowBanned: { $ne: true },
  });
  console.log(`Found ${users.length} users`);
  console.log(JSON.stringify(users.map(x => ({ _id: x._id, email: x.email })), null, 2));

  for (const user of users) {
    await reportLib.shadowBan(user, null, 'banned by cleanup script using email domain');
  }

  await mongoose.disconnect();
})();
