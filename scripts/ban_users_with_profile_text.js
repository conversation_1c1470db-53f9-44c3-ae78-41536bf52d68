const mongoose = require('mongoose');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  await reportLib.banUsersWithProfileText('c a t t y l o p p r');

  console.log('Done');
  await mongoose.disconnect();
})();
