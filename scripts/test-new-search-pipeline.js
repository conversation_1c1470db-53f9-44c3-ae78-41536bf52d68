/* Requirements
Need to enable replicate deployment
Need to create lambda function

MONGODB_URI_RECORDS - Use vector_test db from careneless cluster, this has vector index setup
MONGODB_URI - Use vector_test db from careneless cluster, this has atlas index setup
REPLICATE_KEY
AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY
AWS_S3_REGION
IMAGE_DOMAIN
NODE_ENV=aifilter_carneless
 */

const mongoose = require('mongoose');
const User = require('../models/user');
const Bottleneck = require('bottleneck');
const UserEmbeddings = require('../models/user-embeddings');
const AIFilter = require('../models/ai-filters');
const { generateEmbedding, mergeEmbeddings } = require('../lib/aifilter/helper');
const { IMAGE_DOMAIN, getEmbedConfig } = require('../lib/constants');
const profilesLib = require('../lib/profiles-v3');
const geocoder = require('../lib/geocoder');
const UsersWhoLiked = require('../models/users-who-liked');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const config = getEmbedConfig();

const limiter = new Bottleneck({
  maxConcurrent: config.maxConcurrent,
  minTime: 100,
  reservoir: 600,
  reservoirRefreshAmount: 600,
  reservoirRefreshInterval: 60 * 1000,
});

const rateLimitedGenerateEmbedding = limiter.wrap(generateEmbedding);

async function initGeocoder() {
  await new Promise((resolve, reject) => {
    geocoder.init(
      {
        load: {
          admin1: true,
          admin2: false,
          admin3And4: false,
          alternateNames: false,
        },
      },
      (error) => {
        if (error) {
          // eslint-disable-next-line prefer-promise-reject-errors
          reject('Error initializing geocoder');
        } else {
          console.log('Finished initializing geocoder');
          resolve();
        }
      },
    );
  });
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to database');

    // Clear existing data
    await Promise.all([User.deleteMany({}), UserEmbeddings.deleteMany({}), AIFilter.deleteMany({}), UsersWhoLiked.deleteMany({})]);

    const users = [
      {
        _id: '0',
        countryCode: 'BD',
        latitude: 23.9,
        longitude: 90.4,
        pictures: [
          '8XXfh9mharhBMJtzzMea7OXHJZE2/1723038159127b406114ee3fc9a7e1a612a0b65d64c98.jpg',
          '8XXfh9mharhBMJtzzMea7OXHJZE2/172303817143368e6fb26c235ebb7501cec8be0ea40a1.jpg',
          '8XXfh9mharhBMJtzzMea7OXHJZE2/172303840725032768e8b59be2e854111d4b251af9297.jpg',
          '8XXfh9mharhBMJtzzMea7OXHJZE2/1723038414072b0085080e29d76c777b67cbd928a334f.jpg',
        ],
        preferences: {
          minAge: 18,
          maxAge: 200,
          personality: ['INFP', 'INFJ', 'ENFP', 'ENFJ', 'INTP', 'INTJ', 'ENTP', 'ENTJ', 'ISFP', 'ISFJ', 'ESFP', 'ESFJ', 'ISTP', 'ISTJ', 'ESTP', 'ESTJ'],
          dating: ['female'],
          friends: ['female'],
          showVerifiedOnly: false,
          distance: null,
          keywords: [],
          global: true,
          local: true,
          showUsersOutsideMyRange: true,
        },
        location: {
          type: 'Point',
          coordinates: [90.4, 23.9],
        },
        age: 34,
        recommendationsExhaustedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        birthday: new Date('1990-02-12'),
        appVersion: '1.13.48',
      },
      {
        _id: '1',
        countryCode: 'BD',
        latitude: 23.901,
        longitude: 90.401,
        location: {
          type: 'Point',
          coordinates: [90.401, 23.901],
        },
        profileModifiedAt: new Date(),
        scores: {
          decayedScore2: 3,
        },
        age: 30,
        viewableInDailyProfiles: true,
        hideLocation: false,
        metrics: {
          numPendingReports: 0,
        },
        hideFromNearby: false,
        pictures: [
          'djrcHWvREHSUFNZxHgA0HgUmRQY2/1737530304992a1ee1658930f1aee3632e7cc3fcdf889.jpg',
          'djrcHWvREHSUFNZxHgA0HgUmRQY2/173753045365799605c31b15ed175fa62a4f8231cb7b5.jpg',
        ],
        preferences: {
          minAge: 18,
          maxAge: 200,
          personality: ['INFP', 'INFJ', 'ENFP', 'ENFJ', 'INTP', 'INTJ', 'ENTP', 'ENTJ', 'ISFP', 'ISFJ', 'ESFP', 'ESFJ', 'ISTP', 'ISTJ', 'ESTP', 'ESTJ'],
          dating: [],
          friends: ['female', 'male'],
          showVerifiedOnly: false,
          showUsersOutsideMyRange: true,
          distance: null,
          keywords: [],
          global: true,
          local: true,
          bioLength: null,
          distance2: null,
          maxHeight: null,
          minHeight: null,
          sameCountryOnly: null,
          showToVerifiedOnly: null,
        },
      },
      {
        _id: '2',
        countryCode: 'BD',
        latitude: 23.901,
        longitude: 90.401,
        location: {
          type: 'Point',
          coordinates: [90.401, 23.901],
        },
        profileModifiedAt: new Date(),
        scores: {
          decayedScore2: 3,
        },
        age: 31,
        viewableInDailyProfiles: true,
        hideLocation: false,
        metrics: {
          numPendingReports: 0,
        },
        hideFromNearby: false,
        pictures: ['DKAicW7ctcOTUdQTnWfFdzRMazE2/1683220407008cbceea285a16294776b016e8e3ad0ab4.jpg'],
        preferences: {
          minAge: 18,
          maxAge: 200,
          personality: ['INFP', 'INFJ', 'ENFP', 'ENFJ', 'INTP', 'INTJ', 'ENTP', 'ENTJ', 'ISFP', 'ISFJ', 'ESFP', 'ESFJ', 'ISTP', 'ISTJ', 'ESTP', 'ESTJ'],
          dating: ['male'],
          friends: ['male'],
          showVerifiedOnly: false,
          distance: null,
          keywords: [],
          global: true,
          local: true,
          showUsersOutsideMyRange: true,
        },
      },
      {
        _id: '3',
        countryCode: 'BD',
        latitude: 23.8,
        longitude: 90.3,
        location: {
          type: 'Point',
          coordinates: [90.3798863, 23.8000362],
        },
        profileModifiedAt: new Date(),
        scores: {
          decayedScore2: 3,
        },
        age: 29,
        viewableInDailyProfiles: true,
        hideLocation: false,
        metrics: {
          numPendingReports: 0,
        },
        hideFromNearby: false,
        pictures: ['M6IVLlkEICSUpF4QmQnJWmPSPHd2/1661556111007d7a0197ad8fd517fe9dcc44232f13416.jpg'],
        preferences: {
          minAge: 18,
          maxAge: 200,
          personality: ['INFP', 'INFJ', 'ENFP', 'ENFJ', 'INTP', 'INTJ', 'ENTP', 'ENTJ', 'ISFP', 'ISFJ', 'ESFP', 'ESFJ', 'ISTP', 'ISTJ', 'ESTP', 'ESTJ'],
          dating: ['male'],
          friends: ['male'],
          showVerifiedOnly: false,
          distance: null,
          keywords: [],
          global: true,
          local: true,
          showUsersOutsideMyRange: true,
        },
      },
      {
        _id: '4',
        countryCode: 'PH',
        latitude: 14.6,
        longitude: 121.0,
        location: {
          type: 'Point',
          coordinates: [121.0491, 14.6477],
        },
        profileModifiedAt: new Date(),
        scores: {
          decayedScore2: 3,
        },
        age: 23,
        viewableInDailyProfiles: true,
        hideLocation: false,
        metrics: {
          numPendingReports: 0,
        },
        hideFromNearby: false,
        pictures: [
          'JHxFSlfMUlfFJwMCv9MmhvQzMrJ3/17380359032442b9e25da066ee1a44c9b9a7f52e26577.jpg',
          'JHxFSlfMUlfFJwMCv9MmhvQzMrJ3/1738035922903ae4ec94ddd97f883df9ca56d8c30966e.jpg',
        ],
        preferences: {
          minAge: 24,
          maxAge: 35,
          personality: ['INFP', 'INFJ', 'ENFP', 'ENFJ', 'INTP', 'INTJ', 'ENTP', 'ENTJ', 'ISFP', 'ISFJ', 'ESFP', 'ESFJ', 'ISTP', 'ISTJ', 'ESTP', 'ESTJ'],
          dating: ['male'],
          friends: ['male'],
          showVerifiedOnly: true,
          showUsersOutsideMyRange: true,
          distance: null,
          keywords: [],
          global: true,
          local: true,
          bioLength: null,
          distance2: null,
          maxHeight: null,
          minHeight: null,
          sameCountryOnly: null,
          showToVerifiedOnly: true,
        },
      },
    ];

    const createUserWithEmbeddings = async (user) => {
      const pictureEmbeddings = await Promise.all(
        user.pictures.map((imageKey) => rateLimitedGenerateEmbedding(IMAGE_DOMAIN + imageKey).then((embedding) => ({ key: imageKey, embedding }))),
      );

      const validEmbeddings = pictureEmbeddings.filter((result) => result.embedding);
      const embeddings = validEmbeddings.map((item) => item.embedding);
      const mergedEmbedding = await mergeEmbeddings(embeddings);

      const userEmbedding = new UserEmbeddings({
        user: user._id,
        merged_embedding: mergedEmbedding,
        latitude: user.latitude,
        longitude: user.longitude,
        countryCode: user.countryCode,
        pictureEmbeddings: validEmbeddings,
      });

      await userEmbedding.save();
      await User.create(user);
      console.log('User created', user._id);
    };

    await Promise.all(users.map(createUserWithEmbeddings));
    const usersWhoLiked = new UsersWhoLiked({
      user: '0',
      usersWhoLiked: ['3', '4'],
    });
    await usersWhoLiked.save();

    // Create AIFilter for user 0
    const embedding = await generateEmbedding(null, 'random text');
    if (embedding) {
      const aiFilter = new AIFilter({
        createdBy: '0',
        filter: 'random text',
        filterEmbedding: embedding,
      });

      await aiFilter.save();
      await User.findByIdAndUpdate('0', { $set: { aiFilterPreference: aiFilter._id } });
      console.log('Ai Filter created for user 0');
    }

    await new Promise((resolve) => setTimeout(resolve, 60000)); // Wait for indexing

    const user = await User.findById('0');
    const userAiFilter = await AIFilter.findById(user.aiFilterPreference);

    let profiles = await profilesLib.getVectorSearchResults(user, userAiFilter.filterEmbedding, null, 50);
    console.log('Profiles', profiles); // Will return user 2

    profiles = await profilesLib.getVectorSearchResults(user, userAiFilter.filterEmbedding, null, null, ['PH']);
    console.log('Profiles', profiles); // Will return user 4

    profiles = await profilesLib.getVectorSearchResults(user, userAiFilter.filterEmbedding, ['3', '2'], null, ['BD', 'PH']);
    console.log('Profiles', profiles); // Will return user 4

    await initGeocoder();
    // Will return profile 2, because of vector search score filter
    profiles = await profilesLib.getProfiles(user, 2);
    console.log(
      'Profiles',
      profiles.length,
      profiles.map((x) => x._id),
    );

    // Will return profile 2, because of vector search score filter
    profiles = await profilesLib.getProfiles(user, 10);
    console.log(
      'Profiles',
      profiles.length,
      profiles.map((x) => x._id),
    );
  } catch (error) {
    console.log('Error', error);
  } finally {
    mongoose.disconnect();
    process.exit();
  }
})();
