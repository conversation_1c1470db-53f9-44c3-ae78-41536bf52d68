const fs = require('fs');
const mongoose = require('mongoose');
const { parse } = require('csv-parse/sync');
const Profile = require('../models/profile'); 

const inputFile = `${__dirname}/profiles-image-attribution.csv`;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const BATCH_SIZE = 500;

function isValidURL(url) {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to MongoDB');

    const fileContent = fs.readFileSync(inputFile);
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
    });

    const totalRecords = records.length;
    console.log(`Total records to process: ${totalRecords}`);

    for (let i = 0; i < totalRecords; i += BATCH_SIZE) {
      const batch = records.slice(i, i + BATCH_SIZE);
      const bulkOps = [];
      for (const record of batch) {
        const isImageURLValid = isValidURL(record['Image URL']);
        const isAttributionValid = record['Attribution'] && record['Attribution'] !== 'N/A';
        if (isImageURLValid || isAttributionValid) {
          const updateFields = {};
          if (isImageURLValid) {
            updateFields.image = record['Image URL'];
          }
          if (isAttributionValid) {
            updateFields.imageAttribution = record['Attribution'];
          }
      
          bulkOps.push({
            updateOne: {
              filter: { id: record['Profile Number'] },
              update: { $set: updateFields },
            },
          });
        }
      }
      if (bulkOps.length > 0) {
        await Profile.bulkWrite(bulkOps);
        console.log(`Processed batch: ${i + batch.length}/${totalRecords}`);
      } else {
        console.log(`No valid URLs found in batch: ${i + 1}-${i + BATCH_SIZE}`);
      }
    }

    console.log('All records processed successfully.');
  } catch (error) {
    console.error('Error occurred during processing:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
})();
