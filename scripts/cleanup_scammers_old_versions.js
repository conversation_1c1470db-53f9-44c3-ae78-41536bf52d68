const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  {
    const users = await User.find({
      createdAt: { $gt: new Date('2025-03-29') },
      appVersion: { $in: [ '1.12.16', '1.13.39', '1.13.46' ] },
      shadowBanned: { $ne: true },
    });
    console.log(`Found ${users.length} users on 1.12.16, 1.13.39, or 1.13.46`);
    console.log(JSON.stringify(users.map(x => x._id), null, 2));

    for (const user of users) {
      await reportLib.shadowBan(user, null, 'new signup on old version (banned by cleanup script)', user.appVersion);
    }
  }

  {
    const users = await User.find({
      createdAt: { $gt: new Date('2025-03-29') },
      appVersion: { $in: [ '1.13.76' ] },
      os: { $ne: 'web' },
      shadowBanned: { $ne: true },
    });
    console.log(`Found ${users.length} non-web users on 1.13.76`);
    console.log(JSON.stringify(users.map(x => x._id), null, 2));

    for (const user of users) {
      await reportLib.shadowBan(user, null, 'new signup on old version (soft ban)', user.appVersion);
    }
  }

  console.log('Done');
  await mongoose.disconnect();
})();
