const mongoose = require('mongoose');
const { expect } = require('chai');
const sinon = require('sinon');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../models/user');
const { setDefaultNewNotifications } = require('./set-default-new-notifications')

describe('set default new notifications', () => {
    let mongoServer;
    let connection;
    const numUsers = 5

    before(async () => {
        mongoServer = await MongoMemoryServer.create();
        const mongoUri = mongoServer.getUri();
        connection = await mongoose.connect(mongoUri, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        for (let uid = 0; uid < numUsers; uid++) {
            // Mock User model, left user 0 all notification true
            let userData = { _id: uid }
            if (uid == 1) { // commentLikes false
                userData = {
                    _id: uid,
                    pushNotificationSettings: {
                        commentLikes: false,
                    },
                };
            } else if (uid == 2) { // commentReplies false
                userData = {
                    _id: uid,
                    pushNotificationSettings: {
                        commentReplies: false,
                    },
                };
            } else if (uid == 3) { // friendPosts false
                userData = {
                    _id: uid,
                    pushNotificationSettings: {
                        friendPosts: false,
                    },
                };
            } else if (uid == 4) { // dailyPush
                userData = {
                    _id: uid,
                    pushNotificationSettings: {
                        dailyPush: false,
                    },
                };
            }

            const user = new User(userData);
            await user.save();
            const newUser = await User.findOne({ _id: uid });

        }

    });

    after(async () => {
        await mongoose.disconnect();
        await mongoServer.stop();
    });

    it('set default', async () => {

        await setDefaultNewNotifications()

        // Validate the result
        for (let uid = 0; uid < numUsers; uid++) {
            const user = await User.findOne({ _id: uid });
            console.log(`user ${uid}: `, user.pushNotificationSettings)
            if (uid == 0) { // all true
                expect(user.pushNotificationSettings.commentLikesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentLikesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.dailyFacts).to.equal(true);
                expect(user.pushNotificationSettings.questionOfTheDay).to.equal(true);
                expect(user.pushNotificationSettings.newSoulsNearby).to.equal(true);
                expect(user.pushNotificationSettings.friendStories).to.equal(true);
            } else if (uid == 1) { // commentLikes false
                expect(user.pushNotificationSettings.commentLikesMatches).to.equal(false);
                expect(user.pushNotificationSettings.commentLikesOtherSouls).to.equal(false);
                expect(user.pushNotificationSettings.commentRepliesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.dailyFacts).to.equal(true);
                expect(user.pushNotificationSettings.questionOfTheDay).to.equal(true);
                expect(user.pushNotificationSettings.newSoulsNearby).to.equal(true);
                expect(user.pushNotificationSettings.friendStories).to.equal(true);
            }else if (uid == 2) { // commentReplies false
                expect(user.pushNotificationSettings.commentLikesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentLikesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesMatches).to.equal(false);
                expect(user.pushNotificationSettings.commentRepliesOtherSouls).to.equal(false);
                expect(user.pushNotificationSettings.dailyFacts).to.equal(true);
                expect(user.pushNotificationSettings.questionOfTheDay).to.equal(true);
                expect(user.pushNotificationSettings.newSoulsNearby).to.equal(true);
                expect(user.pushNotificationSettings.friendStories).to.equal(true);
            }else if (uid == 3) { // friendPosts false
                expect(user.pushNotificationSettings.commentLikesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentLikesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.dailyFacts).to.equal(true);
                expect(user.pushNotificationSettings.questionOfTheDay).to.equal(true);
                expect(user.pushNotificationSettings.newSoulsNearby).to.equal(true);
                expect(user.pushNotificationSettings.friendStories).to.equal(false);
            } else if (uid == 4) { // dailyPush false
                expect(user.pushNotificationSettings.commentLikesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentLikesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesMatches).to.equal(true);
                expect(user.pushNotificationSettings.commentRepliesOtherSouls).to.equal(true);
                expect(user.pushNotificationSettings.dailyFacts).to.equal(false);
                expect(user.pushNotificationSettings.questionOfTheDay).to.equal(false);
                expect(user.pushNotificationSettings.newSoulsNearby).to.equal(false);
                expect(user.pushNotificationSettings.friendStories).to.equal(true);
            }
        }

    });
});