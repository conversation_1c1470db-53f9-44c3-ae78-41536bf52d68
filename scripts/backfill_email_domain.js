const mongoose = require('mongoose');
const User = require('../models/user');
const basic = require('../lib/basic');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 500;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');

  let hasMoreData = true;
  let lastId = null;
  let totalProcessed = 0;

  while (hasMoreData) {
    try {
      const filter = {
        ...lastId ? { _id: { $gt: lastId } } : {},
        email: { $exists: true },
        emailDomain: { $exists: false },
      };
      const batch = await User.find(filter).sort({ _id: 1 }).limit(BATCH_SIZE).select({
        _id: 1,
        email: 1,
        emailDomain: 1,
      });

      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      let bulk = User.collection.initializeUnorderedBulkOp();
      for (const user of batch) {
        let emailDomain = basic.extractEmailDomain(user.email);
        console.log(`Processing user ${user._id}, email: ${user.email}, original emailDomain: ${user.emailDomain}, new emailDomain: ${emailDomain}`);
        bulk.find({ _id: user._id }).update({ $set: { emailDomain: emailDomain } });
      }
      await bulk.execute();

      totalProcessed += batch.length;
      lastId = batch[batch.length - 1]._id;

      console.log(`Total processed ${totalProcessed} users`);
    } catch (batchError) {
      console.log(`Error processing batch last id ${lastId}:`, batchError.message);
    }
  }

  console.log('Done');

  await mongoose.disconnect();
  console.log('Disconnected from MongoDB');
  process.exit(0);
})();
