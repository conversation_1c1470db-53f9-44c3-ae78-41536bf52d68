const mongoose = require('mongoose');
const ScammerCleanup = require('../models/scammer-cleanup');

const users = [
  'iiFABKItSAQ9kHkNzMqcisRdLXA3',
  'af5EDnTg9vap6c6jZP9DSgWZFyJ3',
  'iaEnppfJuucoVM7IHuCCWb92WzJ2',
  '4098zjymHsUUSTtePBZch5vHmzI3',
  'zFNlOJfpezYp5lz6dPP4lt17FME2',
  'Vdz6rCWxCMMkv8l3gezKcUtYJSh1',
  'xsCeB4e0PPaWPCHbyRpbd0IPazj2',
  '2g87XymKpMS2WO0rwuVERdAIkNt2',
  'QjMPx4FpP5Ojwm7quoAHSRSDH3D3',
  'wjyyD3WlPHbaWmvVmmRUpZ0IjdI2',
  'Pz68424ndTehYo9JYqFIQSE8aNW2',
  'W6tT9zOWQ9dQVLn8sBEm4NtXLq52',
  '6FSUdMxprdZA4E7Recjun4DX8AZ2',
  'zWb5XRTXPGXrIrjmyTR4UsvmkPa2',
  'z6Si2FQW2qO8YUSPyjzX6YhYv6J3',
  'oX4TggPwEGQU5DcDLhK2iCy39YH2',
  'gmnpGtHnI1UAgBvylxI2zLbTYHu1',
  '1jkekX5HVKdkadoSUhkBIiygl4h1',
  'E6j4NRFvyme1ErEllheBBdDLzXn2',
  '3QcNuypJknh1r8kpgSMKUwk4sn82',
  'Vu7jrgWezVc9PzqypKHJTExWoN52',
  'AoLXVqOf8pemAT17VOm0o5hhwpv1',
  'Lhd1tX1hSUa7AmEm4s2N8iuAzxH2',
  'w1r3Sd8hjtZL4EQLWeRwlaCUuub2',
  'M6AO7CtXYHVzKci0lILsNkvvpNC3',
  'ki8LaqPqiTMiAZBg9vJrAHkRoL13',
  'Vq4DrzjeOaWjEHLoEvJXCBJYcAz1',
  'lzvXiou7ebZDi6F8Iv1GOyrc9Po2',
  'ZWoLMvND90hQLKalF4bZDR4yC8h1',
  'eM6NsGj9bdXPqYrkEYkaymULC103',
  'XyuJvA3RAaUkIapscndp5pIPa6l2',
  '4QtFjHQDEcVIkI1Dq2Uvb6MHDMF3',
];

(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('Connected to database');

  for (const user of users) {
    await ScammerCleanup.deleteOne({ user });
  }

  console.log('Done');
})();
