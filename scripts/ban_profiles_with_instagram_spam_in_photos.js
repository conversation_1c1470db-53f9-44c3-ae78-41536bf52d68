const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');
const constants = require('../lib/constants');
const openai = require('../lib/openai');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    createdAt: { $gt: new Date('2025-03-18') },
    os: 'web',
    emailDomain: { $nin: constants.knownEmailDomains },
    'verification.status': { $in: [ 'unverified', 'rejected' ] },
    shadowBanned: { $ne: true },
    'pictures.0': { $exists: true },
  });
  console.log(`Found ${users.length} users`);

  for (const user of users) {
    console.log(`Processing user ${user._id}`);
    if (await openai.doesImageContainInstagramSpam(user, constants.IMAGE_DOMAIN + user.pictures[0])) {
      await reportLib.shadowBan(user, null, 'unverified with instagram spam in photos (banned by cleanup script)');
    }
  }

  console.log('Done');
  await mongoose.disconnect();
})();
