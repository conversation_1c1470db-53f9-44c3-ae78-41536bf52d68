const mongoose = require('mongoose');
const User = require('../models/user');
const constants = require('../lib/constants');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const res = await User.updateMany({
    createdAt: { $gt: new Date('2025-03-18') },
    os: 'web',
    emailDomain: { $nin: constants.knownEmailDomains },
    'verification.status': { $in: [ 'unverified', 'rejected' ] },
  }, {
    viewableInDailyProfiles: false,
  });
  console.log(res);

  console.log('Done');
  await mongoose.disconnect();
})();
