const mongoose = require('mongoose');
const moment = require('moment');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  {
    const result = await User.updateMany(
      {
        os: 'web',
        createdAt: { $gt: moment().subtract(60, 'days').toDate() },
        'verification.status': { $in: [ 'verified', 'reverifying' ] },
      },
      {
        'verification.status': 'unverified',
      },
    );
    console.log(result);
  }

  console.log('Done');
  await mongoose.disconnect();
})();
