const mongoose = require('mongoose');
const fs = require('fs');
const { parse } = require('csv-parse/sync');
const BannedInfringingText = require('../models/banned-infringing-text');

const FILE = 'banned_infringing_text.csv';

(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('Connected to database');

  const input = fs.readFileSync(FILE);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  console.log(`Read ${records.length} records`);

  await BannedInfringingText.insertMany(records);
  console.log('done');

  await mongoose.disconnect();
})();
