const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    createdAt: { $gt: new Date('2025-03-17') },
    interestNames: { $all: [ 'instagram', 'ciara' ] },
    shadowBanned: { $ne: true },
  });
  console.log(`Found ${users.length} users`);
  console.log(JSON.stringify(users.map(x => ({ _id: x._id, interestNames: x.interestNames })), null, 2));

  for (const user of users) {
    await reportLib.shadowBan(user, null, 'spam in interests (banned by cleanup script)', 'ciara');
  }

  console.log('Done');
  await mongoose.disconnect();
})();
