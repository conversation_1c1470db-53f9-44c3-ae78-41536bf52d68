const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const aws = require('aws-sdk');
const User = require('../models/user');
const Chat = require('../models/chat');
const Message = require('../models/message');
const Comment = require('../models/comment');
const Question = require('../models/question');
const Story = require('../models/story');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

const s3 = new aws.S3({
  accessKeyId: process.env.AWS_S3_KEY,
  secretAccessKey: process.env.AWS_S3_SECRET,
  region: process.env.AWS_S3_REGION,
});

const USER_ID = process.env.USER_ID;
const BUCKET = 'law-enforcement-records';

async function uploadToS3(key, body) {
  const params = {
    Bucket: BUCKET,
    Key: USER_ID + '/' + key,
    Body: JSON.stringify(body, null, 2),
  };
  const response = await s3.putObject(params).promise();
  console.log(response);
}

async function copyS3(file) {
  const params = {
    CopySource: 'cf-simple-s3-origin-boo-prod-408437307763/' + file,
    Bucket: BUCKET,
    Key: USER_ID + '/' + file,
  };
  const response = await s3.copyObject(params).promise();
  console.log(response);
}

(async () => {
  await mongoose.connect(MONGODB_URI);

  {
    const user = await User.findById(USER_ID).select({
      firstName: 1,
      handle: 1,
      location: 1,
      country: 1,
      state: 1,
      city: 1,
      'personality.mbti': 1,
      enneagram: 1,
      gender: 1,
      birthday: 1,
      age: 1,
      height: 1,
      ethnicity: 1,
      horoscope: 1,
      education: 1,
      work: 1,
      description: 1,
      moreAboutUser: 1,
      audioDescription: 1,
      prompts: 1,
      email: 1,
      phoneNumber: 1,
      interestNames: 1,
      languages: 1,
      timezone: 1,
      ipData: 1,
      pictures: 1,
      createdAt: 1,
      premiumExpiration: 1,
      security: 1,
      'verification.status': 1,
      'verification.updatedAt': 1,
      'verification.pictures': 1,
      os: 1,
      osVersion: 1,
      phoneModel: 1,
      deviceId: 1,
      deviceIdHistory: 1,
      webDeviceId: 1,
      appDeviceId: 1,
      deviceLanguage: 1,
      locale: 1,
      countryLocale: 1,
      activeDates: '$metrics.activeDates',
    }).lean();
    await uploadToS3('user_redacted.txt', user);
  }

  {
    const docs = await Comment.find({createdBy: USER_ID}).select({
      createdBy: 1,
      createdAt: 1,
      text: 1,
      vote: 1,
      image: 1,
      audio: 1,
      gif: 1,
      language: 1,
    }).lean();
    await uploadToS3('comments_redacted.txt', docs);
  }
  {
    const docs = await Question.find({createdBy: USER_ID}).select({
      createdBy: 1,
      createdAt: 1,
      title: 1,
      text: 1,
      image: 1,
      images: 1,
      altText: 1,
      audio: 1,
      gif: 1,
      'poll.options': 1,
      interestName: 1,
      hashtags: 1,
      language: 1,
    }).lean();
    await uploadToS3('questions_redacted.txt', docs);
  }
  {
    const docs = await Story.find({createdBy: USER_ID}).select({
      createdBy: 1,
      createdAt: 1,
      image: 1,
      textData: 1,
      visibility: 1,
      backgroundColor: 1,
    }).lean();
    await uploadToS3('stories_redacted.txt', docs);
  }

  await mongoose.disconnect();
})();
