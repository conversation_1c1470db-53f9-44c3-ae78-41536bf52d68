const mongoose = require('mongoose');
const BanAppeal = require('../models/ban-appeal');
const User = require('../models/user');
const { banReasonMapping } = require('../lib/report');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 1000;

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const multiAccountReasons = banReasonMapping.find(([category]) => category === 'creating multiple accounts')?.[1];
    const underageReasons = ['underage birthday'];

    let totalAppealsReviewed = 0;
    let lastProcessedId = null;
    let hasMore = true;
    const appealBulkOps = [];
    const userBulkOps = [];

    while (hasMore) {
      const query = {
        bannedReasons: { $in: multiAccountReasons },
        decision: { $exists: false },
      };

      if (lastProcessedId) {
        query._id = { $gt: lastProcessedId };
      }

      const appeals = await BanAppeal.find(query, '_id user').sort({ _id: 1 }).limit(BATCH_SIZE).populate('user', 'deviceId').lean();
      if (appeals.length === 0) {
        hasMore = false;
        console.log(`No more appeals to process.`);
        break;
      }
      lastProcessedId = appeals[appeals.length - 1]._id;
      totalAppealsReviewed += appeals.length;

      const deviceIdAppealMap = new Map();

      for (const appeal of appeals) {
        const user = appeal.user;
        // eslint-disable-next-line no-continue
        if (!user || !user.deviceId) continue;

        if (!deviceIdAppealMap.has(user.deviceId)) {
          deviceIdAppealMap.set(user.deviceId, []);
        }
        deviceIdAppealMap.get(user.deviceId).push({ appealId: appeal._id, userId: user._id });
      }

      const deviceIds = Array.from(deviceIdAppealMap.keys());
      const bannedUsers = await User.find(
        {
          deviceId: { $in: deviceIds },
          shadowBanned: true,
          $or: [{ bannedReason: { $in: underageReasons } }, { bannedReasons: { $in: underageReasons } }],
        },
        'deviceId',
      ).lean();

      const bannedDeviceIds = new Set(bannedUsers.map((u) => u.deviceId));
      for (const deviceId of bannedDeviceIds) {
        const appealsToReject = deviceIdAppealMap.get(deviceId) || [];

        for (const { appealId, userId } of appealsToReject) {
          appealBulkOps.push({
            updateOne: {
              filter: { _id: appealId },
              update: {
                $set: {
                  decision: 'rejected',
                },
              },
            },
          });

          userBulkOps.push({
            updateOne: {
              filter: { _id: userId },
              update: {
                $set: { 'banNotice.appealStatus': 'rejected' },
                $push: {
                  banHistory: {
                    action: 'appealRejected',
                    by: null,
                    date: Date.now(),
                  },
                },
              },
            },
          });
        }
      }

      console.log(`Processed batch ending at ${lastProcessedId}, totalAppealsReviewed: ${totalAppealsReviewed}, queued ${appealBulkOps.length} appeal updates and ${userBulkOps.length} user updates.`);
    }

    if (appealBulkOps.length) {
      console.log(`Writing ${appealBulkOps.length} appeal updates...`);
      console.log(appealBulkOps.map(x => x.updateOne.filter._id));
      if (process.env.APPLY_WRITES) {
        await BanAppeal.bulkWrite(appealBulkOps);
      }
    }

    if (userBulkOps.length) {
      console.log(`Writing ${userBulkOps.length} user updates...`);
      console.log(userBulkOps.map(x => x.updateOne.filter._id));
      if (process.env.APPLY_WRITES) {
        await User.bulkWrite(userBulkOps);
      }
    }

    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
