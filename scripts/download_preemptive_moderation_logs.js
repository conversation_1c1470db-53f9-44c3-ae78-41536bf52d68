const mongoose = require('mongoose');
const fs = require('fs');
const { stringify } = require('csv-stringify/sync');
const PreemptiveModerationLog = require('../models/preemptive-moderation-log');

const users = [
  'prysQdvarFUi9k9Ld4JnNLOPsD12',
  'kp1qw08hV0bTr3423DURP5mKuSy2',
  'sseR7G7erETeGDN2cZWHxigZfgt1',
  '8wGXpltKJjWEyFeFcDLbreYmwez2',
  'WWU0mSNhCGd5ZYoEKzn5mSGagYj1',
  'ta0qxVcTwiMLFmbyaMqXGlRL22F3',
  '5A0hRPPFLPN9ohvbXVIRP5EBznQ2',
  'cJaOQVq8FJZiCjZIoGSJJJ3VEtZ2',
  '7kp2FuIU0rW4e7hme7O9uydbjpf1',
  'QNQG0Q8oFvTubrsTKVctfZErcl63',
];

(async () => {
  const records = [];
  for (const user of users) {
    const query = { reportedUser: user };
    const projection = {
      reportedUser: 1,
      createdAt: 1,
      prompt: '$openai.prompt',
      output: '$openai.output',
      decision: 1,
      banReason: 1,
    };
    const record = await PreemptiveModerationLog.findOne(query, projection).sort('-createdAt').lean();
    if (record) {
      records.push(record);
    }
  }

  const output = stringify(records, {header: true});
  const outputFile = `${__dirname}/prompts.csv`;
  fs.writeFileSync(outputFile, output);
})();
