const mongoose = require('mongoose');
const PurchaseReceipt = require('../models/purchase-receipt');

(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('Connected to database');

  let results = await PurchaseReceipt.aggregate([
    // Sort by user and purchaseDate in ascending order
    // (If you have the { user: 1, purchaseDate: 1 } index,
    //  you can rely on the index scan to provide sorted data,
    //  but it's still good practice to be explicit.)
    { $sort: { user: 1, purchaseDate: 1 } },

    // exclude refunds and non-ios purchases
    {
      $match: {
        isRefund: { $ne: true },
        service: 'apple',
      }
    },

    // Compute the maximum expiration date of *all previous* receipts per user
    {
      $setWindowFields: {
        partitionBy: "$user",
        sortBy: { purchaseDate: 1 },
        output: {
          maxPrevExpiration: {
            $max: "$expirationDate",
            // 'documents: ["unbounded", -1]' means
            // "from the beginning of this partition up to (but not including) this document"
            window: { documents: ["unbounded", -1] }
          }
        }
      }
    },

    // Keep only documents where the current purchase date
    // + 1 day is still <= (i.e., strictly less than or equal to)
    // an older subscription's expiration date.
    //
    // That means the new purchase (this doc) occurred
    // at least 1 day before the older subscription ended.
    {
      $match: {
        $expr: {
          $gte: [
            "$maxPrevExpiration",
            {
              $dateAdd: {
                startDate: "$purchaseDate",
                unit: "day",
                amount: 1
              }
            }
          ]
        }
      }
    },

    // Group by user to get a unique list of users who have
    // purchased a new sub >= 1 day prior to an older sub's expiration
    { $group: { _id: "$user" } },

    // Output in a simpler form: { user: "<userId>" }
    { $project: { _id: 0, user: "$_id" } }
  ]);

  results = results.map(x => x.user);
  console.log(JSON.stringify(results, null, 2));

  console.log('Done');
})();
