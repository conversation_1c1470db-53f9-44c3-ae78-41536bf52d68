// Set your secret key. Remember to switch to your live secret key in production.
// See your keys here: https://dashboard.stripe.com/apikeys
const stripe = require('../lib/stripe').stripe;

(async () => {
  let i = 0;
  for await (const invoice of stripe.invoices.list({ status: 'open' })) {
    console.log(invoice);
    const createdAt = invoice.created * 1000;
    const numMillsecondsElapsed = Date.now() - createdAt;
    const numHoursElapsed = numMillsecondsElapsed / 3600000;
    console.log('DATE', typeof invoice.created, new Date(createdAt), numHoursElapsed, numHoursElapsed >= 24);
    if (i++ > 25) {
      break;
    }
  }
})();
