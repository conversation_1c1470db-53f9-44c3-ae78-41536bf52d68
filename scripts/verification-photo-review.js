/*
ENV Required:
ANTHROPIC_KEY
OPENAI_KEY
IMAGE_DOMAIN
GEMINI_API_KEY
TOGETHER_API_KEY
MONGODB_URI

Models used:
- OpenAI: gpt-4o, gpt-4o-mini
- Claude: claude-3-7-sonnet-20250219, claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022, claude-3-opus-20240229
- Together: meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo
- Gemini: gemini-1.5-pro
*/

const mongoose = require('mongoose');
const fs = require('fs').promises;
const axios = require('axios');
const User = require('../models/user');
const { getOpenaiClient } = require('../lib/openai-client');
const { getClaudeApiClient } = require('../lib/claude-client');
const { getTogetherAiClient } = require('../lib/together-client');
const { IMAGE_DOMAIN } = require('../lib/constants');
const { OpenAI, ClaudeAPI } = require('../lib/prompt');
// eslint-disable-next-line import/no-unresolved
const { stringify } = require('csv-stringify/sync');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

const prompt = `Analyze the provided image and determine how likely it is to have artificial borders (such as unnatural black or colored frames distinctly different from the background) or cropped body parts (especially arms cut off at the edges). Ignore natural shadows and lighting effects. You must respond with only a confidence score between 0.00 and 1.00 without any explanation or additional text.`;

const scammersId = [
  '2KOwv5dvK1VOBhxo3CQr6QVzNgp2',
  'bxLPI8hbejNmKoph19aK2X9oUxA2',
  'KOog3ZkTkJdqrURqeO59Gx31ni13',
  'N56WZcz9Jgbkog8da9h0rLtpAev1',
  'ZqeFj5R2NXdaXRN8oeZdTNeKNWB3',
  'wdPJOJTmaDUOdgHGT3srgOViO1v2',
  'W5cdRsxxYaQ81XtWmoXnf6Bbnst1',
  'ex6mbUL8PpO0mcHWF12E7cEhUiL2',
  'w9GC5RqJLxMPEPs6jy2IMphXR7s2',
  'TnQzMUX6ZQgdtXBBHbC5xIGVx1H3',
  '9VeXVvurwbec3ygn5M4ewmYurI13',
  'Jviv4jAhjTe2BtjvREndfbpQcCb2',
  'UWc2SwU8Q0ei8rHLOKE9hmOZvj12',
  'UIu09zUTQWZ8LWZhaZ8OivxYOmS2',
  'RH2De0gNKiO96uEOXzq49TkWQtL2',
  'WiVUyXs9fPWOUo0iFOCUWUAxMzv1',
  'ooN0zD4JvOWT02yAitMu80O9w3D2',
  'dFjOyshm5hV9bFXy8auooZkmMJe2',
  '2OrlAx3sXXZqkhDBMJwE4o11gkw2',
  'u6LkHu9ajvaQlP5zigrITScUvNu2',
];

const getClients = () => ({
  openai: getOpenaiClient(),
  claude: getClaudeApiClient(),
  together: getTogetherAiClient(),
});

const models = {
  openai: ['gpt-4o', 'gpt-4o-mini'],
  claude: ['claude-3-7-sonnet-20250219', 'claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229'],
};

const getLlamaResponse = async (client, url) => {
  const model = 'meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo';

  try {
    const response = await client.chat.completions.create({
      model,
      messages: [
        {
          role: 'user',
          content: [
            { type: 'text', text: prompt },
            { type: 'image_url', image_url: { url } },
          ],
        },
      ],
    });

    const output = response?.choices?.[0]?.message?.content || 'No response';
    const cost = ((response?.usage?.total_tokens ?? 0) * 1.2) / 1_000_000;

    console.log(`${model} response:`, output);

    return {
      [`${model} response`]: output,
      [`${model} cost`]: cost,
    };
  } catch (err) {
    return {
      [`${model} response`]: `Error: ${err.message}`,
      [`${model} cost`]: 0,
    };
  }
};

const getGeminiResponse = async (imageUrl) => {
  const model = 'gemini-1.5-pro';

  const API_URL = `https://generativelanguage.googleapis.com/v1/models/${model}:generateContent?key=${GEMINI_API_KEY}`;

  try {
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const base64Image = Buffer.from(imageResponse.data).toString('base64');

    const requestBody = {
      contents: [
        {
          parts: [{ inline_data: { mime_type: 'image/jpeg', data: base64Image } }, { text: prompt }],
        },
      ],
      generationConfig: {
        temperature: 1,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 4096,
      },
      safetySettings: [
        { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
        { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
      ],
    };

    const response = await axios.post(API_URL, requestBody, {
      headers: { 'Content-Type': 'application/json' },
    });

    const output = response?.data?.candidates?.[0]?.content?.parts[0]?.text || 'No valid response';
    const inputCost = ((response?.data?.usageMetadata?.promptTokenCount || 0) * 1.25) / 1_000_000;
    const outputCost = ((response?.data?.usageMetadata?.candidatesTokenCount || 0) * 5) / 1_000_000;
    const totalCost = inputCost + outputCost;

    console.log(`${model} response:`, output);

    return {
      [`${model} response`]: output,
      [`${model} cost`]: totalCost,
    };
  } catch (err) {
    return {
      [`${model} response`]: `Error: ${err.response?.data?.error?.message || err.message}`,
      [`${model} cost`]: 0,
    };
  }
};

const getResults = async (imageUrl) => {
  const clients = getClients();

  const params = { prompt, image_urls: [imageUrl] };

  const runModels = (client, Provider, visionModels) =>
    Promise.all(
      visionModels.map(async (model) => {
        const response = await new Provider(client, model).executePrompt(params);
        console.log(`${model} response:`, response.output);
        return { [`${model} response`]: response.output, [`${model} cost`]: response.cost };
      }),
    );

  const responses = await Promise.all([runModels(clients.openai, OpenAI, models.openai), runModels(clients.claude, ClaudeAPI, models.claude)]);
  const togetherResponse = await getLlamaResponse(clients.together, imageUrl);
  const geminiResponse = await getGeminiResponse(imageUrl);

  return Object.assign({}, ...responses.flat(), togetherResponse, geminiResponse);
};

const processUsersBatch = (users) =>
  Promise.all(
    users.map(async (user) => {
      try {
        const imageKey = user.verification.pictures.at(-1);
        if (imageKey && (imageKey.endsWith('.jpg') || imageKey.endsWith('.jpeg'))) {
          const results = await getResults(`${IMAGE_DOMAIN}${imageKey}`);
          return { userId: user._id, imageUrl: `${IMAGE_DOMAIN}${imageKey}`, ...results };
        }
        return null;
      } catch (err) {
        console.log(`Error processing user ${user._id}:`, err);
        return null;
      }
    }),
  );

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const [scammers, verifiedUsers] = await Promise.all([
      User.find({ _id: { $in: scammersId } })
        .select('_id verification')
        .lean(),
      User.find({ _id: { $nin: scammersId }, 'verification.status': 'verified' })
        .sort({ createdAt: -1 })
        .select('_id verification')
        .limit(100)
        .lean(),
    ]);

    const users = [...scammers, ...verifiedUsers];
    const results = [];

    for (let i = 0; i < users.length; i += 10) {
      console.log(`Processing batch ${i / 10 + 1}/${Math.ceil(users.length / 10)}`);
      const batchResults = await processUsersBatch(users.slice(i, i + 10));
      results.push(...batchResults.filter(Boolean));
      await fs.writeFile('verification_photo_review_result.csv', stringify(results, { header: true }));
      console.log('CSV file saved');
    }
  } catch (err) {
    console.log('Error:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
