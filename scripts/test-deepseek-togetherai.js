const { getTogetherAiClient } = require('../lib/together-client');

const together = getTogetherAiClient();

/*
    To use the DeepSeek-R1 model, can use the following parameters:
    model: 'deepseek-ai/DeepSeek-R1',

    For DeepSeek-V3 model, max_tokens can be set upto 12288
    For DeepSeek-R1 model, max_tokens can be set upto 32768

    repetition_penalty, stop and stream are same for both models
*/

const getDeepSeekResponse = async (prompt, stream) => {
  try {
    const response = await together.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: 'deepseek-ai/DeepSeek-R1',
      repetition_penalty: 1,
      stop: ['<｜end▁of▁sentence｜>'],
      stream,
    });

    if (stream) {
      let content = '';
      let usage = null;
      let stopReceived = false;

      for await (const token of response) {
        if (token.choices[0]?.finish_reason === 'stop') {
          stopReceived = true;
        } else if (stopReceived && token.usage) {
          usage = token.usage;
        } else {
          content += token.choices[0]?.delta?.content || '';
        }
      }

      const tokens = usage ? usage.prompt_tokens + usage.completion_tokens : 0;
      return { content, tokens };
    }

    // Non-streaming response handling
    const { choices, usage } = response;
    const content = choices?.[0]?.message?.content ?? '';
    const tokens = (usage?.prompt_tokens ?? 0) + (usage?.completion_tokens ?? 0);

    return { content, tokens };
  } catch (error) {
    console.log('Error in response generation:', error);
    return { content: `Error in response generation: ${error.message}`, tokens: 0 };
  }
};

(async () => {
  const prompt = `Help me talk with my match on Boo, a personality-based social dating app, in language "ja". Provide 4 topical icebreakers for me to send to my match, formatted as a JSON array in the following format: { "output": ["example 1", "example 2", "example 3", "example 4"] }. Don't assume anything not stated in the following sentences.
  
        Me: Rahul, 29 M, INTJ, Taurus, Dating/Friends, My location Pune, My interests: psychology, gadgets, philosophy, memes, dogs.
  
        Match: Kate, 39 F, INFJ, Virgo, Dating/Friends, Their location: Indonesia, Their interests: animals, relationship advice, psychology.
  
        Ensure the response is in language "ja".`;

  const response = await getDeepSeekResponse(prompt, true);
  console.log(JSON.stringify(response, null, 2));
})();

/* Response with stream set to false:
DeepSeek-V3 model:
{
  "id": "90e1a658da72c86b",
  "object": "chat.completion",
  "created": 1738913174,
  "model": "deepseek-ai/DeepSeek-V3",
  "prompt": [],
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"output\": [\n    \"こんにちは、Kate！心理学に興味があるみたいですね。最近読んだ本や記事で印象に残っているものはありますか？\",\n    \"動物が好きなんですね！私も犬が大好きです。飼っているペットはいますか？\",\n    \"関係のアドバイスに興味があるとのこと。人間関係で大切にしていることは何ですか？\",\n    \"INTJとINFJということで、私たちの性格タイプは似ているかもしれません。MBTIについてどう思いますか？\"\n  ]\n}\n```",
        "tool_calls": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 160,
    "total_tokens": 290,
    "completion_tokens": 130
  }
}

DeepSeek-R1 model:
{
  "id": "90e1c07f2ce6c866",
  "object": "chat.completion",
  "created": 1738914275,
  "model": "deepseek-ai/DeepSeek-R1",
  "prompt": [],
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "<think>\nOkay, let me tackle this query. The user wants to help talk with their match on Boo using Japanese. They provided their info and the match's info. The goal is to create 4 icebreakers in Japanese as a JSON array.\n\nFirst, I need to analyze the common interests. Both are into psychology, which is a good starting point. Rahul likes gadgets, memes, dogs, and philosophy. Kate is into animals, relationship advice, and psychology. Since Kate is in Indonesia, maybe ask about animal-related experiences there. Since they both like psychology, perhaps ask about book recommendations. Also, they mentioned relationship advice for Kate, so maybe incorporate that. Dogs and animals are a common point. Using zodiac signs (Taurus and Virgo) could be another angle.\n\nI need to make sure each icebreaker touches on a shared interest or a point from their profiles. Also, questions should be open-ended to encourage a response. Translate them into Japanese, keeping the context natural. Check for proper politeness and phrasing. Ensure the JSON format is correct with the response as an array under \"output\".\n</think>\n\n```json\n{\n  \"output\": [\n    \"心理学に興味があるんですね！最近読んだオススメの本や理論があれば教えて欲しいです🐶（共通の趣味が嬉しいです！）\",\n    \"インドネシアの動物保護活動について何か関わった経験はありますか？ぜひ話を聞いてみたいです🐾\",\n    \"INTJとINFJって相性が良いらしいですけど、人間関係のアドバイスで大切にしていることって何ですか？✨\",\n    \"犬が好きとのことですが、もし飼うとしたらどんな犬種を選びますか？私は柴犬が大好きなんです🐕\"\n  ]\n}\n```",
        "tool_calls": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 169,
    "total_tokens": 555,
    "completion_tokens": 386
  }
}
  */
