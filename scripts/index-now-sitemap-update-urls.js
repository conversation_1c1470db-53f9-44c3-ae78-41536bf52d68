const fs = require('fs');
const { parse } = require('csv-parse/sync');
const axios = require('axios');
const CSV_FILE_PATH = 'index-now-url-list.csv';
const BATCH_SIZE = 10000; // Maximum URLs per request

const API_KEY = 'ed5e1668f023405bae547a4d3474fc30';
const SEARCH_ENGINE_ENDPOINT = 'https://api.indexnow.org/indexnow';

async function submitUrls(urls) {
    if (urls.length === 0) {
        console.log('No URLs found to submit.');
        return;
    }

    for (let i = 0; i < urls.length; i += BATCH_SIZE) {
        const batch = urls.slice(i, i + BATCH_SIZE);
        try {
            const response = await axios.post(SEARCH_ENGINE_ENDPOINT, {
                host: 'boo.world',
                key: API_KEY,
                keyLocation: `https://boo.world/${API_KEY}.txt`,
                urlList: batch
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            console.log(`Success: Submitted ${batch.length} URLs - Status: ${response.status}`);
        } catch (error) {
            console.error(`Error submitting batch of URLs:`, error.response?.data || error.message);
        }
    }
}

(async function loadCsvAndUpdateIndex() {
    try {
        console.log('Reading CSV file...');
        const fileContent = fs.readFileSync(CSV_FILE_PATH, 'utf-8');
        const records = parse(fileContent, {
            delimiter: ',',
            columns: true,
            skip_empty_lines: true,
            relax_quotes: true,
          });
        const urls = records.map(row => row.URL).filter(Boolean);
        console.log(`Loaded ${urls.length} URLs from CSV file`);
        console.log('Submitting URLs to IndexNow API')
        await submitUrls(urls);
        console.log('Indexing process completed.');
    } catch (error) {
        console.error('Error reading CSV file:', error.message);
    }
})();
