const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    createdAt: { $gt: new Date('2025-01-08') },
    shadowBanned: true,
    bannedNotes: 'undefined',
  });
  console.log(`Num users: ${users.length}`);

  let count = 0;
  let promisesArr = [];
  for (const user of users) {
    promisesArr.push(reportLib.unban(user, null, 'undo mistaken auto-ban'));
    if (promisesArr.length == 100) {
      await Promise.all(promisesArr);
      count = count + 100;
      console.log(`Completed ${count}`);
      promisesArr = [];
    }
  }
  if (promisesArr.length) {
    await Promise.all(promisesArr);
  }

  console.log('done');
  await mongoose.disconnect();
})();
