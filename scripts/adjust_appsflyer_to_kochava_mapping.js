const mongoose = require('mongoose');
const User = require('../models/user');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const query = {
    createdAt: { $gt: new Date('2025-02-01') },
    'appsflyer.payload.media_source': { $exists: true },
  };

  const pipeline = [
    {
      $set: {
        'kochava.network': '$appsflyer.payload.media_source',
      }
    },
  ];

  const res = await User.updateMany(query, pipeline);
  console.log(res);

  console.log('Done');
  await mongoose.disconnect();
})();
