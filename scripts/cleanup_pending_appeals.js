const mongoose = require('mongoose');
const BanAppeal = require('../models/ban-appeal');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 1000;

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    let totalAppealsReviewed = 0;
    let lastProcessedId = null;
    let hasMore = true;
    const appealBulkOps = [];

    while (hasMore) {
      const query = {
        decision: { $exists: false },
      };

      if (lastProcessedId) {
        query._id = { $gt: lastProcessedId };
      }

      const appeals = await BanAppeal.find(query, '_id user').sort({ _id: 1 }).limit(BATCH_SIZE).populate('user', 'banNotice').lean();
      if (appeals.length === 0) {
        hasMore = false;
        console.log(`No more appeals to process.`);
        break;
      }
      lastProcessedId = appeals[appeals.length - 1]._id;
      totalAppealsReviewed += appeals.length;

      for (const appeal of appeals) {
        const user = appeal.user;
        if (user && !user.banNotice) {
          appealBulkOps.push({
            updateOne: {
              filter: { _id: appeal._id },
              update: {
                $set: {
                  decision: 'dismissed',
                  notes: 'user was unbanned before appeal was reviewed',
                },
              },
            },
          });
        }
      }

      console.log(`Processed batch ending at ${lastProcessedId}, totalAppealsReviewed: ${totalAppealsReviewed}, queued ${appealBulkOps.length} appeal updates.`);
    }

    if (appealBulkOps.length) {
      console.log(`Writing ${appealBulkOps.length} appeal updates...`);
      console.log(appealBulkOps.map(x => x.updateOne.filter._id));
      if (process.env.APPLY_WRITES) {
        await BanAppeal.bulkWrite(appealBulkOps);
      }
    }

    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
