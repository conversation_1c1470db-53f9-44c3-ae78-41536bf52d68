const fs = require('fs');
const mongoose = require('mongoose');
const { parse } = require('csv-parse/sync');
const ScammerCleanup = require('../models/scammer-cleanup');

function getRandomInt(max) {
  // return random int between 1 and max, inclusive
  return 1 + Math.floor(Math.random() * max);
}

function getQueueNumbers() {
  const first = getRandomInt(7);
  let second = first;
  while (second == first) {
    second = getRandomInt(7);
  }
  return [ first, second ];
}

(async () => {
  if (process.env.INSERT) {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to database');
  }

  const fileContent = fs.readFileSync('users_for_verification_cleanup_processed_border_detected.csv', 'utf8');
  const records = parse(fileContent, { columns: true, skip_empty_lines: true });
  const recordsMapped = records.map(function(record) {
    const queueNumbers = getQueueNumbers();
    return {
      user: record._id,
      verificationPhoto: record.fullUrl,
      review1: {
        queue: queueNumbers[0],
      },
      review2: {
        queue: queueNumbers[1],
      },
    }
  });
  console.log(recordsMapped);

  if (process.env.INSERT) {
    await ScammerCleanup.insertMany(recordsMapped);
  }

  console.log('Done');
})();
