/*
Required Env:
- MONGODB_URI_RECORDS
- MONGODB_URI
- REPLICATE_KEY
- AWS_KEY
- AWS_SECRET
- AWS_S3_REGION
- IMAGE_DOMAIN
- REPLICATE_MODEL_TYPE (custom/public) default custom

Need to create VectorSearch Index for UserEmbeddings collection:
{
  "fields": [
    {
      "numDimensions": 768,
      "path": "merged_embedding",
      "similarity": "cosine",
      "type": "vector"
    },
    {
      "path": "user",
      "type": "filter"
    },
    {
      "path": "countryCode",
      "type": "filter"
    },
    {
      "path": "latitude",
      "type": "filter"
    },
    {
      "path": "longitude",
      "type": "filter"
    },
    {
      "path": "hideFromVisionSearch",
      "type": "filter"
    }
  ]
}
*/
require('log-timestamp');
const mongoose = require('mongoose');
const Bottleneck = require('bottleneck');
const User = require('../models/user');
const UserEmbeddings = require('../models/user-embeddings');
const { generateEmbedding, mergeEmbeddings } = require('../lib/aifilter/helper');
const { IMAGE_DOMAIN, getEmbedConfig } = require('../lib/constants');

const BATCH_SIZE = 1000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const config = getEmbedConfig();

const limiter = new Bottleneck({
  maxConcurrent: config.maxConcurrent,
  minTime: 12,
  reservoir: 5000,
  reservoirRefreshAmount: 5000,
  reservoirRefreshInterval: 60 * 1000,
});

const rateLimitedGenerateEmbedding = limiter.wrap(generateEmbedding);

async function processUser(user) {
  try {
    const startTime = Date.now();
    const pictures = user.pictures || [];

    let pictureEmbeddings = [];
    const requestPromises = pictures
      .filter((imageKey) => ['.jpg', '.jpeg', '.png'].some((ext) => imageKey.endsWith(ext)))
      .map((imageKey) => rateLimitedGenerateEmbedding(IMAGE_DOMAIN + imageKey).then((embedding) => pictureEmbeddings.push({ key: imageKey, embedding })));
    await Promise.all(requestPromises);
    pictureEmbeddings = pictureEmbeddings.filter((result) => result.embedding);

    const embeddings = pictureEmbeddings.map((item) => item.embedding);

    if (!embeddings.length) {
      console.log(`No embeddings generated for user ${user._id}`);
      return;
    }

    // Merge embeddings only if there are multiple
    const mergedEmbedding = embeddings.length === 1 ? embeddings[0] : await mergeEmbeddings(embeddings);

    await UserEmbeddings.findOneAndUpdate(
      { user: user._id },
      {
        $set: {
          merged_embedding: mergedEmbedding,
          pictureEmbeddings,
          latitude: user.latitude,
          longitude: user.longitude,
          countryCode: user.countryCode,
          source: 'backfill_v3',
          hideFromVisionSearch: user.hideFromVisionSearch,
        },
      },
      { upsert: true },
    );

    console.log(`Processed user ${user._id} in ${Date.now() - startTime}ms`);
  } catch (error) {
    console.log(`Error processing user ${user._id}: ${error.message}`);
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    let hasMore = true;
    let lastId = null;
    let totalProcessed = 0;

    // Fetch last processed user
    const lastProcessed = await UserEmbeddings.findOne({ source: 'backfill_v3' }).sort({ user: 1 });
    if (lastProcessed) {
      lastId = lastProcessed.user;
    }

    while (hasMore) {
      try {
        console.time('Batch processing time');

        const filter = {
          ...(lastId && { _id: { $lt: lastId } }),
          'verification.status': 'verified',
          createdAt: {
            $gte: new Date('2023-03-25T00:00:00.000Z'),
            $lt: new Date('2025-03-25T00:00:00.000Z'),
          },
          $expr: { $gt: [{ $size: '$pictures' }, 0] },
          gender: { $in: ['male', 'non-binary'] },
        };

        const users = await User.find(filter).sort({ _id: -1 }).limit(BATCH_SIZE).select('_id pictures latitude longitude countryCode').lean();

        if (users.length === 0) {
          hasMore = false;
          break;
        }

        // get total number of pictures
        const totalPictures = users.reduce((acc, user) => acc + user.pictures.length, 0);
        console.log(`Processing ${users.length} users with ${totalPictures} pictures`);

        await Promise.all(users.map(processUser));
        totalProcessed += users.length;
        lastId = users[users.length - 1]._id;
        console.log(`Total processed: ${totalProcessed} users`);
      } catch (batchError) {
        console.log(`Error processing batch with last id ${lastId}:`, batchError.message);
      } finally {
        console.timeEnd('Batch processing time');
      }
    }
  } catch (e) {
    console.log('Error connecting to MongoDB:', e.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
