const User = require('../models/user');

async function setDefaultNewNotifications(){
    try {
    // Conditional updates using an aggregation pipeline
    const result = await User.updateMany(
        {
          $or: [
            { 'pushNotificationSettings.friendPosts': false },
            { 'pushNotificationSettings.commentLikes': false },
            { 'pushNotificationSettings.commentReplies': false },
            { 'pushNotificationSettings.dailyPush': false },
          ]
        }, // Match all users, conditions will be checked in the pipeline
        [
          {
            $set: {
              'pushNotificationSettings.friendStories': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.friendPosts', false] },
                  false,
                  '$pushNotificationSettings.friendStories', // Keep existing value
                ],
              },
              'pushNotificationSettings.commentLikesMatches': {
                $cond: [
                  {  $eq: ['$pushNotificationSettings.commentLikes', false] },
                  false,
                  '$pushNotificationSettings.commentLikesMatches', // Keep existing value
                ],
              },
              'pushNotificationSettings.commentLikesOtherSouls': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.commentLikes', false] },
                  false,
                  '$pushNotificationSettings.commentLikesOtherSouls', // Keep existing value
                ],
              },
              'pushNotificationSettings.commentRepliesMatches': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.commentReplies', false] },
                  false,
                  '$pushNotificationSettings.commentRepliesMatches', // Keep existing value
                ],
              },
              'pushNotificationSettings.commentRepliesOtherSouls': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.commentReplies', false] },
                  false,
                  '$pushNotificationSettings.commentRepliesOtherSouls', // Keep existing value
                ],
              },
              'pushNotificationSettings.dailyFacts': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.dailyPush', false] },
                  false,
                  '$pushNotificationSettings.dailyFacts', // Keep existing value
                ],
              },
              'pushNotificationSettings.questionOfTheDay': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.dailyPush', false] },
                  false,
                  '$pushNotificationSettings.questionOfTheDay', // Keep existing value
                ],
              },
              'pushNotificationSettings.newSoulsNearby': {
                $cond: [
                  { $eq: ['$pushNotificationSettings.dailyPush', false] },
                  false,
                  '$pushNotificationSettings.newSoulsNearby', // Keep existing value
                ],
              },
            },
          },
        ]
      );
  
      console.log(`Users updated: ${result.modifiedCount}`);
    } catch (error) {
      console.error('Error updating user settings:', error);
    }
}

module.exports = {
    setDefaultNewNotifications
}