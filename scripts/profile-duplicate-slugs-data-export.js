require('log-timestamp');
const fs = require('fs');
const mongoose = require('mongoose');
const { stringify } = require('csv-stringify/sync');
const Profile = require('../models/profile');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const OUTPUT_FILENAME = 'profile-duplicate-slugs-data-export.csv';

(async function exportDuplicateSlugs() {
  try {
    console.log('Start of the Script')
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Database Connected')
    const pipeline = [
      {
        $group: {
          _id: "$slug",
          count: { $sum: 1 },
          profileIds: { $addToSet: "$id" },
          profileNames: { $addToSet: "$name" }
        }
      },
      {
        $match: {
          count: { $gt: 1 }
        }
      },
      {
        $sort: {
          count: -1
        }
      },
      {
        $project: {
          slug: "$_id",
          count: 1,
          profileIds: 1,
          profileNames: 1,
          _id: 0
        }
      }
    ];

    const duplicates = await Profile.aggregate(pipeline);

    if (duplicates.length === 0) {
      console.log('No duplicate slugs found.');
      return;
    }

    const csvData = duplicates.map(item => ({
      slug: item.slug,
      duplicateCount: item.count,
      profileIds: item.profileIds.join(', '),
      profileNames: item.profileNames.join(', ')
    }));

    const csv = stringify(csvData, {
      header: true,
      columns: [
        { key: 'slug', header: 'Slug' },
        { key: 'profileNames', header: 'Profile Names' },
        { key: 'duplicateCount', header: 'Duplicate Count' },
        { key: 'profileIds', header: 'Profile IDs' },
      ]
    });

    fs.writeFileSync(OUTPUT_FILENAME, csv);
    console.log(`Exported ${csvData.length} profile duplicates to ${OUTPUT_FILENAME} complete...`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
  }
})();
