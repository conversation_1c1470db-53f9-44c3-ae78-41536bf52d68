/* env required:
HIVE_VISUAL_MODERATION_API_KEY
URL
*/

const superagent = require('superagent');
const constants = require('../lib/constants');

const URL = process.env.URL || 'https://images.beta.boo.dating/samples/safe.mov';
(async () => {
  const res = await superagent
    .post('https://api.thehive.ai/api/v2/task/sync')
    .set('authorization', `token ${process.env.HIVE_VISUAL_MODERATION_API_KEY}`)
    .field('url', URL);

  for (const output of res.body.status[0].response.output) {
    for (const x of output.classes) {
      if (x.score >= 0.75 && !(x.class.includes('no_') || x.class.includes('not_'))) {
        console.log(x);
      }
    }
  }
})();
