const mongoose = require('mongoose');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const appealsToUpdate = await ProfileTempBanAppeal.aggregate([
      {
        $match: {
          decision: { $exists: false }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDoc'
        }
      },
      {
        $unwind: '$userDoc'
      },
      {
        $match: {
          $or: [
            { 'userDoc.shadowBanned': { $ne: true } },
            {
              $and: [
                { $expr: { $ne: ['$profileTempBanReportId', '$userDoc.profileTempBanReportId'] } },
                { profileTempBanReportId: { $exists: true } },
                { 'userDoc.profileTempBanReportId': { $exists: true } },

              ]
            }
          ]
        }
      },
      {
        $project:{
          _id:1,
          user: "$userDoc._id",
          profileTempBanReportId: 1,
          userDocProfileTempBanReportId: "$userDoc.profileTempBanReportId",
          shadowBanned: "$userDoc.shadowBanned",
        }
      }
    ]);

    console.log(`Found ${appealsToUpdate.length} appeals to dismiss`);

    if (appealsToUpdate.length === 0) {
      console.log('No appeals need to be dismissed. Exiting.');
      return;
    }

    let processedCount = 0;

    for (const doc of appealsToUpdate) {
      if (process.env.APPLY_WRITES) {
        // Update the appeal
        await ProfileTempBanAppeal.updateOne(
          { _id: doc._id },
          {
            $set: {
              decision: 'dismissed',
              notes: 'user was unbanned before appeal was reviewed'
            }
          }
        );

        // Update the user if they have pending appeal status
        const user = await User.findById(doc.user);
        if (user && user.accountRestrictions?.profileTempBan?.appealStatus === 'pending') {
          user.accountRestrictions.profileTempBan = undefined;
          user.banHistory.push({
            action: 'ProfileTempBanAppealDismissed',
            date: Date.now(),
            notes: 'user was unbanned before appeal was reviewed',
          });
          await user.save();
        }
      }

      processedCount++;
      if (processedCount % 100 === 0) {
        console.log(`Processed ${processedCount}/${appealsToUpdate.length} appeals`);
      }
    }

    if (process.env.APPLY_WRITES) {
      console.log(`Successfully processed ${processedCount} appeals`);
    } else {
      console.log('DRY RUN: Set APPLY_WRITES=true to actually perform the updates');
      console.log(`Would process ${processedCount} appeals`);
    }

    console.log(`\n=== CLEANUP SUMMARY ===`);
    console.log(`Total appeals to be dismissed: ${appealsToUpdate.length}`);
    console.log(`Appeals that remain pending: ${pendingAppeals - appealsToUpdate.length}`);
    
    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
