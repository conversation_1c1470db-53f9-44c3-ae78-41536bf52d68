/**
 * Profile Temp Ban Appeals Cleanup Script
 *
 * This script cleans up pending profile temp ban appeals that should have been
 * automatically dismissed but weren't due to the implementation gap before
 * commit 7b86d346c6655e88fb93d61f7a992c68aa88e181.
 *
 * The script dismisses appeals in the following cases:
 * 1. User no longer exists
 * 2. User is already unbanned (no longer shadow banned or no profile temp ban reason)
 * 3. Appeal's profileTempBanReportId doesn't match user's current profileTempBanReportId
 *    (indicating this is an outdated appeal for a previous profile temp ban)
 *
 * Usage:
 *   # Dry run (default - shows what would be changed without making changes)
 *   node scripts/cleanup_profile_temp_ban_appeals.js
 *
 *   # Actually perform the cleanup
 *   APPLY_WRITES=true node scripts/cleanup_profile_temp_ban_appeals.js
 *
 *   # Use with specific database
 *   MONGODB_URI=mongodb://your-db-uri node scripts/cleanup_profile_temp_ban_appeals.js
 */

const mongoose = require('mongoose');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 1000;

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
    console.log(`Database: ${MONGODB_URI}`);

    // Safety check for production
    if (MONGODB_URI.includes('production') || MONGODB_URI.includes('prod')) {
      console.log('\n⚠️  WARNING: This appears to be a production database!');
      console.log('This script will dismiss profile temp ban appeals that should have been auto-dismissed.');
      console.log('Make sure you understand the impact before proceeding.');
      console.log('Set APPLY_WRITES=true to actually perform the updates.');
      console.log('');
    }

    // First, get overall statistics
    const totalAppeals = await ProfileTempBanAppeal.countDocuments();
    const pendingAppeals = await ProfileTempBanAppeal.countDocuments({ decision: { $exists: false } });

    console.log(`Found ${totalAppeals} total profile temp ban appeals`);
    console.log(`Found ${pendingAppeals} pending profile temp ban appeals`);

    if (pendingAppeals === 0) {
      console.log('No pending appeals to process. Exiting.');
      return;
    }

    let totalAppealsReviewed = 0;
    let totalAppealsDismissed = 0;
    let userUnbannedCount = 0;
    let outdatedAppealCount = 0;
    let userNotFoundCount = 0;
    let lastProcessedId = null;
    let hasMore = true;
    const appealBulkOps = [];
    const userBulkOps = [];

    while (hasMore) {
      const query = {
        decision: { $exists: false }, // Only pending appeals
      };

      if (lastProcessedId) {
        query._id = { $gt: lastProcessedId };
      }

      const appeals = await ProfileTempBanAppeal.find(query)
        .sort({ _id: 1 })
        .limit(BATCH_SIZE)
        .populate('user', 'shadowBanned profileTempBanReason profileTempBanReportId banHistory accountRestrictions')
        .lean();

      if (appeals.length === 0) {
        hasMore = false;
        console.log(`No more appeals to process.`);
        break;
      }

      lastProcessedId = appeals[appeals.length - 1]._id;
      totalAppealsReviewed += appeals.length;

      for (const appeal of appeals) {
        const user = appeal.user;
        let shouldDismiss = false;
        let dismissalReason = '';

        if (!user) {
          // User doesn't exist anymore
          shouldDismiss = true;
          dismissalReason = 'user no longer exists';
          userNotFoundCount++;
        } else {
          // Check if user is already unbanned (no longer shadow banned or no profile temp ban reason)
          if (!user.shadowBanned || !user.profileTempBanReason) {
            shouldDismiss = true;
            dismissalReason = 'user was unbanned before appeal was reviewed';
            userUnbannedCount++;
          }
          // Check if profileTempBanReportId doesn't match (indicating this is an outdated appeal)
          else if (appeal.profileTempBanReportId && user.profileTempBanReportId &&
                   appeal.profileTempBanReportId.toString() !== user.profileTempBanReportId.toString()) {
            shouldDismiss = true;
            dismissalReason = 'appeal is for a previous profile temp ban (outdated)';
            outdatedAppealCount++;
          }
        }

        if (shouldDismiss) {
          totalAppealsDismissed++;
          
          // Queue appeal update
          appealBulkOps.push({
            updateOne: {
              filter: { _id: appeal._id },
              update: {
                $set: {
                  decision: 'dismissed',
                  notes: dismissalReason,
                },
              },
            },
          });

          // Queue user update if user exists and has pending appeal status
          if (user && user.accountRestrictions?.profileTempBan?.appealStatus === 'pending') {
            userBulkOps.push({
              updateOne: {
                filter: { _id: user._id },
                update: {
                  $unset: {
                    'accountRestrictions.profileTempBan': 1,
                  },
                  $push: {
                    banHistory: {
                      action: 'ProfileTempBanAppealDismissed',
                      date: new Date(),
                      notes: dismissalReason,
                    },
                  },
                },
              },
            });
          }
        }
      }

      const progressPercent = ((totalAppealsReviewed / pendingAppeals) * 100).toFixed(1);
      console.log(`Processed batch ending at ${lastProcessedId} | Progress: ${totalAppealsReviewed}/${pendingAppeals} (${progressPercent}%) | Dismissed: ${totalAppealsDismissed}`);
    }

    // Execute bulk operations
    if (appealBulkOps.length > 0) {
      console.log(`Writing ${appealBulkOps.length} appeal updates...`);
      if (process.env.APPLY_WRITES) {
        await ProfileTempBanAppeal.bulkWrite(appealBulkOps);
        console.log(`Successfully updated ${appealBulkOps.length} appeals`);
      } else {
        console.log('DRY RUN: Set APPLY_WRITES=true to actually perform the updates');
        console.log('Sample appeal IDs that would be updated:', appealBulkOps.slice(0, 10).map(op => op.updateOne.filter._id));
      }
    }

    if (userBulkOps.length > 0) {
      console.log(`Writing ${userBulkOps.length} user updates...`);
      if (process.env.APPLY_WRITES) {
        await User.bulkWrite(userBulkOps);
        console.log(`Successfully updated ${userBulkOps.length} users`);
      } else {
        console.log('DRY RUN: Set APPLY_WRITES=true to actually perform the updates');
        console.log('Sample user IDs that would be updated:', userBulkOps.slice(0, 10).map(op => op.updateOne.filter._id));
      }
    }

    console.log(`\n=== CLEANUP SUMMARY ===`);
    console.log(`Total appeals reviewed: ${totalAppealsReviewed}`);
    console.log(`Total appeals dismissed: ${totalAppealsDismissed}`);
    console.log(`  - User no longer exists: ${userNotFoundCount}`);
    console.log(`  - User already unbanned: ${userUnbannedCount}`);
    console.log(`  - Outdated appeals (mismatched reportId): ${outdatedAppealCount}`);
    console.log(`Appeals that remain pending: ${totalAppealsReviewed - totalAppealsDismissed}`);
    
    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
