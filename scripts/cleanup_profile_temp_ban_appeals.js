const mongoose = require('mongoose');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const appealsToUpdate = await ProfileTempBanAppeal.aggregate([
      {
        $match: {
          decision: { $exists: false }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDoc'
        }
      },
      {
        $unwind: '$userDoc'
      },
      {
        $match: {
          // here do match for which we need to update docs to dismiss
          $or: [
            { 'userDoc.shadowBanned': { $ne: true } },
            { 'userDoc.profileTempBanReason': { $exists: false } },
            {
              $and: [
                { profileTempBanReportId: { $exists: true } },
                { 'userDoc.profileTempBanReportId': { $exists: true } },
                { $expr: { $ne: ['$profileTempBanReportId', '$userDoc.profileTempBanReportId'] } }
              ]
            }
          ]
        }
      }
    ]);

    const totalAppealsDismissed = appealsToUpdate.length;
    console.log(`Found ${totalAppealsDismissed} appeals to dismiss`);

    if (totalAppealsDismissed === 0) {
      console.log('No appeals need to be dismissed. Exiting.');
      return;
    }

    // Count dismissal reasons
    const userNotFoundCount = appealsToUpdate.filter(a => a.dismissalReason === 'user no longer exists').length;
    const userUnbannedCount = appealsToUpdate.filter(a => a.dismissalReason === 'user was unbanned before appeal was reviewed').length;
    const outdatedAppealCount = appealsToUpdate.filter(a => a.dismissalReason === 'appeal is for a previous profile temp ban (outdated)').length;

    console.log(`Breakdown:`);
    console.log(`  - User no longer exists: ${userNotFoundCount}`);
    console.log(`  - User already unbanned: ${userUnbannedCount}`);
    console.log(`  - Outdated appeals: ${outdatedAppealCount}`);

    // Prepare bulk operations
    const appealBulkOps = appealsToUpdate.map(appeal => ({
      updateOne: {
        filter: { _id: appeal._id },
        update: {
          $set: {
            decision: 'dismissed',
            notes: appeal.dismissalReason,
          },
        },
      },
    }));

    const userBulkOps = appealsToUpdate
      .filter(appeal => appeal.userDoc && appeal.userDoc.accountRestrictions?.profileTempBan?.appealStatus === 'pending')
      .map(appeal => ({
        updateOne: {
          filter: { _id: appeal.user },
          update: {
            $unset: {
              'accountRestrictions.profileTempBan': 1,
            },
            $push: {
              banHistory: {
                action: 'ProfileTempBanAppealDismissed',
                date: new Date(),
                notes: appeal.dismissalReason,
              },
            },
          },
        },
      }));

    // Execute bulk operations
    if (appealBulkOps.length > 0) {
      console.log(`Writing ${appealBulkOps.length} appeal updates...`);
      if (process.env.APPLY_WRITES) {
        await ProfileTempBanAppeal.bulkWrite(appealBulkOps);
        console.log(`Successfully updated ${appealBulkOps.length} appeals`);
      } else {
        console.log('DRY RUN: Set APPLY_WRITES=true to actually perform the updates');
        console.log('Sample appeal IDs that would be updated:', appealBulkOps.slice(0, 10).map(op => op.updateOne.filter._id));
      }
    }

    if (userBulkOps.length > 0) {
      console.log(`Writing ${userBulkOps.length} user updates...`);
      if (process.env.APPLY_WRITES) {
        await User.bulkWrite(userBulkOps);
        console.log(`Successfully updated ${userBulkOps.length} users`);
      } else {
        console.log('DRY RUN: Set APPLY_WRITES=true to actually perform the updates');
        console.log('Sample user IDs that would be updated:', userBulkOps.slice(0, 10).map(op => op.updateOne.filter._id));
      }
    }

    console.log(`\n=== CLEANUP SUMMARY ===`);
    console.log(`Total appeals dismissed: ${totalAppealsDismissed}`);
    console.log(`  - User no longer exists: ${userNotFoundCount}`);
    console.log(`  - User already unbanned: ${userUnbannedCount}`);
    console.log(`  - Outdated appeals (mismatched reportId): ${outdatedAppealCount}`);
    console.log(`Appeals that remain pending: ${pendingAppeals - totalAppealsDismissed}`);
    
    console.log('All cleanup operations complete.');
  } catch (err) {
    console.log('Error during cleanup:', err);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
