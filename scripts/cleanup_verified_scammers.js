require('log-timestamp');
const fs = require('fs');
const { parse } = require('csv-parse/sync');
const { stringify } = require('csv-stringify/sync');
const mongoose = require('mongoose');
const User = require('../models/user');
const openai = require('../lib/openai');

(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('cleanup job: Connected to database');

  const csvFile = 'users_for_verification_cleanup.csv';
  const content = fs.readFileSync(csvFile);
  const users = parse(content, { delimiter: ',', columns: true, skip_empty_lines: true });
  console.log(`cleanup job: num users: ${users.length}`);

  let promises = [];
  let batch = 0;
  let numSuspicious = 0;
  let numNotSuspicious = 0;
  let numSkip = 0;

  async function processUser(user) {
    user.verification = {
      pictures: [user.verificationPicture],
    };
    user.isSuspicious = await openai.isVerificationPhotoSuspicious(user, true) ? 1 : 0;
    delete user.verification;
    if (user.isSuspicious) {
      numSuspicious++;
    }
    if (!user.isSuspicious) {
      numNotSuspicious++;
    }
  }

  let startTime = new Date();
  for (const user of users) {
    if (user.isSuspicious !== undefined) {
      numSkip++;
      continue;
    }
    promises.push(processUser(user));
    if (promises.length >= 50) {
      await Promise.all(promises);
      const output = stringify(users, {header: true});
      fs.writeFileSync(csvFile, output);
      const msForBatch = new Date() - startTime;
      batch++;
      console.log(`cleanup job: batch: ${batch}, numSuspicious: ${numSuspicious}, numNotSuspicious: ${numNotSuspicious}, numSkip: ${numSkip}, msForBatch: ${msForBatch}`);
      promises = [];
      startTime = new Date();
      if (msForBatch < 1000) {
        // ensure max rate of 50 requests per second
        await new Promise((r) => setTimeout(r, 1000 - msForBatch));
      }
    }
  }

  console.log('cleanup job: Done');
  await mongoose.disconnect();
})();
