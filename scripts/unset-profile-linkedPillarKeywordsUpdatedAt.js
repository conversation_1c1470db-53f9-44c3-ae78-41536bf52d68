const mongoose = require('mongoose');
const Profile = require('../models/profile');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const res = await Profile.updateMany(
    {},
    [
      {
        $unset: ['linkedPillarKeywordsUpdatedAt'],  // Use array of strings for $unset in aggregation pipeline
      },
    ]
  );
  console.log(res);

  await mongoose.disconnect();
})();
