const { decodeTransactions } = require("app-store-server-api");
const appStoreConnectLib = require('../lib/app-store-connect');

(async () => {

  /*
  const api = appStoreConnectLib.getApi();

  const originalTransactionId = '310002003202290'
  const response = await api.getTransactionHistory(originalTransactionId);
  console.log(response);

  const transactions = await decodeTransactions(response.signedTransactions)
  console.log(transactions);
  */


  const originalTransactionId = '310002003202290'
  const transactionId = '310002003202290'

  const response = await appStoreConnectLib.getTransaction(originalTransactionId, transactionId);
  console.log(response);
  console.log(response.price, response.currency, response.storefront);
})();

/*
[
  {
    transactionId: '310002003202290',
    originalTransactionId: '310002003202290',
    webOrderLineItemId: '310000955495974',
    bundleId: 'enterprises.dating.boo',
    productId: 'infinity_m3_v3_x3_d50',
    subscriptionGroupIdentifier: '20679156',
    purchaseDate: 1731506056000,
    originalPurchaseDate: 1731506057000,
    expiresDate: 1739454856000,
    quantity: 1,
    type: 'Auto-Renewable Subscription',
    inAppOwnershipType: 'PURCHASED',
    signedDate: 1739436987651,
    offerType: 1,
    environment: 'Production',
    transactionReason: 'PURCHASE',
    storefront: 'SGP',
    storefrontId: '143464',
    price: 15000,
    currency: 'SGD',
    offerDiscountType: 'PAY_UP_FRONT'
  },
  {
    transactionId: '310002142991918',
    originalTransactionId: '310002003202290',
    webOrderLineItemId: '310000955495975',
    bundleId: 'enterprises.dating.boo',
    productId: 'infinity_m3_v3_x3_d50',
    subscriptionGroupIdentifier: '20679156',
    purchaseDate: 1739454856000,
    originalPurchaseDate: 1731506057000,
    expiresDate: 1747140856000,
    quantity: 1,
    type: 'Auto-Renewable Subscription',
    inAppOwnershipType: 'PURCHASED',
    signedDate: 1739436987651,
    environment: 'Production',
    transactionReason: 'RENEWAL',
    storefront: 'SGP',
    storefrontId: '143464',
    price: 29990,
    currency: 'SGD'
  }
]
*/
