const fs = require('fs');
const { stringify } = require('csv-stringify/sync');
const mongoose = require('mongoose');
const { OpenAI } = require('../lib/prompt');
const openaiClient = require('../lib/openai-client');
const User = require('../models/user');

// env needed:
// - MONGODB_URI
// - OPENAI_KEY

const userIds = [
  'PxmEpJB9KGTVnCTkRgHS4cFikFT2',
  'tP3uLobDTmNw5hSsQPPeuj925bY2',
  'AXjE2AJkQ7SxIHJJo6v89LZjq4C2',
  'K6ZvoL9XXsXx44vHl5otWbtXlBq2',
  'xu5bTMWxeMOrLTPy4NB5a8kyccV2',
  '7UNLc9Wfidg1zn7INtoRjcuBjGC2',
  'Mq63b3jEobP5hFCNS15f6n4Mue13',
  'Pr1O5qRkktZDfNB4vsyXTeVof2s1',
  '23zYBLqELMck6NvhCqBg1ZBPuGh1',
  'ptWLyCHFUMdvJsLDmi1jmfmeRxw2',
  'pk62C3uJOkOgd7uVQ5Cf1ETasM83',
  'ITl5fD0pqCWhrz2VNi10vHClDPu1',
  'AYEltrFFpJQmV0MtzCqdXDuqZBg2',
  '0MXfGH893ca6cWMQftafeheRv0b2',
  'aTjfJ6XepPhdBgQy9eTs6IS5ezL2',
  '5houR4FPHKewbm1wDlgpuZOu5OW2',
  'AYGdUZhSjuNziijqAiofIc7Vbz02',
  'OE3DfhxD27Qb6MaaQUI7DCcr9aK2',
  'Oej4Yy5KexMAJ3J3ydR3CMSepQ83',
  'ADRJ3qWxjOd31OawsPQImKbJWX03',
  'J3wolLBLphVtGTg0juAm19u4AZI2',
  'QUWAZ1DTvEZJAPT7H6714X35hib2',
  'qKVQd1AEzVSKzeOrlPEFxXfZZj13',
  'qgwayF4hKnZWasm9aVy5xqQeD1B3',
  'AHRa323bnpdn850Z5iCcH5aakRH3',
  'HLUFr0LYgSfSTDhlycH3boWH9Xd2',
  'jjgBqSOsGSe5GQczWBBYHMWFg2e2',
  'aLhnRho42eWUM9PUhNk1pvX0tci2',
  'VnVx6DOqsvbvQOr1pKDxZvlQry23',
  'nvOUSWnUaNOcvYX4xdPDdozqsts2',
  'rmMIB6k7dMXPOPMmNOrC5nCzDMl1',
  'V0Duw6MEnNOIcXgBIvLLXuRFPlA3',
  'GkbXsDqZGIcYDVNTZtH9dHzJJg63',
  'msRiZt48RnasHJ8owYKsN2OCDQM2',
  'i88K1ZiTOoYJOym18TRhCGZsMEi1',
  'AOcGhfP1ZWTVj0LHxnC90Ah2AnF3',
  'AkGIptG0kTQyUZt7yEdPtxiM6Z23',
  'QxIpusRLAtbAerBGjHe2B8EVICm1',
  'F6iFt8Zu25YJB8FYOdlwqpyqLdp1',
  'OL27d8V1dHZ8G4d5xTTXbnmRZfs1',
  'EZ7BuoVQ7WZAWCSF5OaKN9Avwv53',
  '4L5iDfXyTNbnWizrJ7WqEQdoLtA2',
  '7CpPk8CLbZREgakgDuOBfn9zw2E2',
  'gj7WCPdRqWUeX1LmHDp4fBfsAH62',
  'uMg2bJ93wmU8wDc8KSAwidKwL662',
  'NujLoJ2sXkeQpkcQ8JDIiXJ3G9o2',
  '2wixOO3ZhZa0xlryrarL7QJXt382',
  '0mjbgLBhuThiFCzbHhqXo3tL1fa2',
  'HbcEM8LJ0reskjO7t7zv0LgnDxz2',
  'vIPgKEePqjV7o75RPCAsNYtPNCD2',
  'HvscUyDOyGNGWlqMgrvV8bNAQp62',
  'WxqSaj8R4YNsOVtb0VN7knaofJx2',
  '83Qnu7BR5JQ27BZyJPDVsHWqQ472',
  'vuG3mpUPQPTIep1sWaVUUAMvOg32',
  'I7vSAsrcUeR877CEcI0CCoutY562',
  'SZcBm9ilWHRitKJjrgGZnsfoY6q2',
  'Pz36NHzypPbLqKwLMXeE9V3SS1E3',
  '5Zopw7VxoKY6XDXdmHuTSflg3wm1',
  'yo1AU0gNI2akUuxsDaXBe95g0L52',
  'pTqgLwI9aUMl6cNfYXxy2Y4VSS02',
  'YhHzoVQfxLgDE1xmtKy6SNq9MSJ2',
  'BV7KQYmguJMuzOYSAXxJymCFtJw2',
  'BLshdvDBiiZv4jiGdsOvRzX8S2B3',
  '5OeeICqihycDmQcOIxl9hEltOE73',
  'e6vEpzWUnQZOyMwI2MlfbGx32fx1',
  'aKF8DgUWoISLyvboxHCBoCqWEM13',
  't8orJrITX4h3FWcmKsUqNQtaiI23',
  'U18b2xaaZsRx6uzgw1Vf09v2SjJ3',
  'FYM2FhRaCQeVq38f7RRMFpPztF83',
  'z42XxcdSRdWt7tYFPMQvMpRkRoJ3',
  'YFiHqRYLi2g86D8j2HhdEqsJq682',
  'Ke8wSgIWFLaxyv8mbrF7wbBK1lf1',
  'HHBhnk3CPdSjN6BgYKleTvDOi1g1',
  '865EWtiZOcfnsUyG9QFg6X8JXCD3',
  'vgfsQWd1ixNxJwiAkG3izzvxx3w1',
  'LvSdTBc4SEOaYtW3BUWcky3J8zA3',
  'TxkoBOBTOSUQTRC25cOi3hpOMXS2',
  'uAJDLILiwmQHzIxyECvEFzGRYn13',
  '865EWtiZOcfnsUyG9QFg6X8JXCD3',
  'fruGAPLcRQPHPPK98liuJp5iP542',
  'LtX9cpplKHSDylH82gZKkV76tIu2',
  'ZN7xrzng8zUmxu5a5EJl7jR5Z0O2',
  'D1CJ5VsBeOYVxkfo9aHvnXKuX3N2',
  '6euqLcxfVYTtZI0DHfFtu5sAEyA2',
  'iRIlGqrJ92N6FrxqD5XnRqrd2UI2',
  'ceL0RZl5yzg3AUAEhAAXfU2Vf7A2',
  'dE1Ykz1Cdnc2wFigKt88PxAV4Dh1',
  'tP3uLobDTmNw5hSsQPPeuj925bY2',
  'mW03a8pWpgRPNJJYbzAIOZhqaiE3',
  'YkS2ptcFgjSmCoibJJV34EKOuIe2',
  'EGRlatFWKMYlFV6LT8Mb6x4a0qw2',
  'zcacgDxfWybUNR2HOoWSeI2Gds12',
  'd6sFsQQ5z6SGkct9Kg3UBPFjmfn2',
  'yP9nKEo61eQWcNlDP4tLQo9OHRD2',
  'AI5VctlEiVdlcLbIR8WcTn3bZco2',
  'fsumGVuoVsfW13tVXpMTy004sw82',
  'iYek9l9VFaYkX3FRwEkvum4ehXt1',
  'n9iUbZEHjrRVehbchqJi9jkeFxQ2',
  '6PYXZ9SdoqgnHak854gft7tK75d2',
  '8TwtJBvhqGbfrQm3QU7yh8fUWQi2',
  'l729hshFRShKgesBPLJcf8HaIQ03',
  '0r5eSbnQD3giCjxsHNKindTy2EY2',
  'oYAfnCvblaezstXdYgDvkJaJX5E3',
  'SZLpWK7YQEXdGILTPKKGtDi2cAw1',
  'RzBnLWl0scV6bPu9Iyea1gXz5463',
  'sJCRZ2TBEycuB5lulSDXDzSeVhH3',
  'wIAi1jWDo4Tamf7vDEGjZ2ezWeG2',
  'rvPIxGOuSXM6nAkFGByFe0ksGvf1',
  'okZgkKbZryUJzu2hpJulac9NDlH3',
  'SXtNW8gCq7XQDjrJ22XVAvc90mz2',
  '4njk1D7UMFYqGhh24ZtUpz1MV872',
  'nzHewbubGPe89sj6OLEJEDXAaTK2',
  'zcLgBTmt3NQZLqqYXO7jhYRnz0C2',
  'C8e3F9P0r9Ns6RXuy0auuA6Fm4f1',
  'ub2Eh1yNiJXcnL3Az9S7yalNYA33',
  'bpk47Aod4MSVKHdSpRS96mZWu4o1',
  'R2qJpOfREpSp5B1tYXBYB9wYkW63',
  'tu4ZRsZYbFg9Jv0PHemWc724LjE2',
  'cGn2Exw3dgNIMmR4dBaR7fzObeC3',
  'm1Vq5o3qvWV1pP6ItLepHqADSTY2',
  'fl3WSENk9RhzFtPNCTAdDVXSOao2',
  'Opgc5NsFgaaVkDdYfsdL2nEoBAn1',
  'vfmX8uSb5tZo8BWkPfJmfFFJEx13',
  'OnzFfZFcGyNzX40VrIPdACL6e4h1',
  'z5Xfc9xfOahRLIx91SDhEvHl18n1',
  'dcb2YmsqsxVrmDIihfRHlbqBcc82',
  'Cu3bXgA4gedzuay0GXVzwlar55z1',
  'RFrbHi1Ccraqewc5tkV395PmyXA2',
  'PCTdVyp0rLarU4KQgJHd3Gn05Cm2',
  'joag13ew7bgNUTX27VTPuwlJXlS2',
  'AFmM9shRJrhy4tmD5vPOXNv8tXk2',
  'Da0FrigLVQRjdmmLrBsOFjabiDG3',
  '7WjfNPqjMcO0KJi3eDg1HH6P2bl1',
  'eTe1Yhjhk4XtAa1NqAhyHaH05w13',
  'ZoCSLWxbn4ajW5D9TRevdYgSsC33',
  'Xe065gMQrkaJKaWPkxXb4Axlc3g2',
  'lzPo8nC4yAetyqa8FdAJqo4VXIx2',
  'tUqSEsLLAIOasDgJ5DOvSWc2Nrq1',
  'rvY7lDuzFMSdnjEsvCDte2s17rf2',
  'hQ9lwTHoGiPSIbYeuiamO6sNbJI2',
  'PrrSTSqlYXUPSNOa0P35T4UM5ek2',
  '0VusjC72DmgyRGRV7hR4u9e9ysu1',
  'UyHl6RInbif0WIatUtU0ksamPdI2',
  'RIXell7eDeZnycvI44ZAVjy0TuZ2',
  'OwF5x0diOpe3CE47kGzd9MGKEaz2',
  '7ZVnpTeKvqW3PpUoGcdNfvv3IWC3',
  'OME8a5EZeMWOPgKyd18A1Yn70c43',
  'YNg4Hw8e0TflDSdh7SUdVLJSU1G3',
  'JpOmgHokTVcv8mVnKNCcml6urLn1',
  'wmbkrHPQgDPTVcNdCLJXDuwUJnX2',
  'gkHKD9I4hYdlHHZ5eY2EULMPv7n2',
  '1qQHEGgGPafOjfCNZ0IhN6BYCTn1',
  'SlTUJHgxZwQIYzIldhfc1wwX9bQ2',
  '5taYXtkE2rcWOICxeltWeceL6MR2',
  'aW3e8cpYU9RQ8mxZFeKAzg8oL8c2',
  'EPBhnWCHhpN15P8CuSsEOHaOKTO2',
  '8gznf6zz5Xgz9U4XJwbcyGV268x1',
  'FICVVCeLYQfbuoHK8erWjFDvOdf1',
  'La4ytVTEY2ZvYVSMSuecS8Tg0yx2',
  'URh4lGCv9WaiRGYrVKtVZ8xlr4x1',
  'lVXiHOQyrng8o0lsXHBZPGp3tI73',
  'oSLwC9QVihb4Ay2GY53vbd3aZuX2',
  '0Par4eiirRXSWowL0KsTewXtXgh2',
  'ioBhsJZ1I5Qrz9bPDnsA3CYCdD82',
  'oNiRjtyoLcNmrw016QGry3779w93',
  'xqQ0GMAuxXZ8AP91GMfdlls7PvV2',
  'Kpyx8yt15mbqrpdlqKwgoboyEFN2',
  '7Y9WwOCOnQhvglQgWlbpCRTlner1',
  'Cmw89HywP7XKDZI5q92fQtGuYXt2',
  '6tU8yepiBZbn9OonnXA5V3J9cBl1',
  'VM6LX4xj61Mons9TPnI82wEZoUs2',
  'w5YI9jT13hgGq6JrPFYDgWwftey2',
  'BTEsH0NojJhPweyKNghE0JLjox33',
  '7vKcwLyXqVV1RibkrXdVJWjZ1ec2',
  'YTEsSP3iTyYtw248iH7Su0sWXh43',
  'LRPSIxvC48hdEKvZ6MEjPvct4772',
  'WfZEd4WzeVVkCtkqsHmBqBw5pAF3',
  'EZiIyt4hDkTFRlMoSBaAmnnptL92',
  'KX06NeWhuoaJNhuYUJKN9LBfzdf2',
  'AEeU8TjvbwUkkQxAQfNwAJlp8Ck1',
  'zrTn6G3FMTeTUbHS6VDsUlOfsSM2',
  'dhKead2wsVZYag7iXxeFYF8TyQ32',
  'zXQAmqlzaAUlOEPuD1hN8TZ4UPW2',
  '2Z9xcOzlXEbCjhxhLgnwB0NDGAH2',
  'rK95GPZbjacJc9PucPJaequGDiS2',
  's15Qf5vB65SVSK8pi5tVyB1K3VM2',
  'Gf7IN8WXL2fSThnlFeiNjposA7R2',
  'vpTYem4CzMcChQgnuEkqHk4e8l42',
  '2COxy4HOGzbFocoKUi6PWCW3Ng33',
  '7kdZy9r6pbaTnnFlGZMtKxlnZzo2',
  'uIu5MuGW5ufhdH6lc0GXjNlA97A3',
  'qH0xmmFGwvhTfjwUdFbslZcHlh53',
  'PLjW7MDlFsUE2BTk2pCKcvo6baM2',
  'yGn1clqrKbhHUcHBj4nYM0D6rAF3',
  'Dk1CKphtmYVPqqMPSRyMNdlfEvm1',
  'Mlbr1DTTLPfR5FATmoHgtwJK8Pm2',
  'bJWgM9bObdTc4VRXg7bqjbqFXUC2',
  'WnKyaEsRfmM6oQyxZjqWNmaIgzg2',
  'ewYyr8l05qTzO2zhFY7Ct5yXQ7j2',
  'Ee8E2g74preBE50NOch48KJJ6Ao1',
  'QPw1u0sssOblfMJvCifN4rAPqlW2',
  'vDO2VfG81LhsTvEnCgNGbT0Y6Jq1',
  'AGDpsWSj0NTUABVciAamgXVWTqP2',
  '8ikFllNUFDOxfImphJP5Dl52cUL2',
  '5FDpVmsJMff7U3JW5SM0rCTLBTc2',
  '1dubq38YzRMcoaGNG01bXj46iSw2',
  'JdYhRspcHvMU9L0uxlPe1ofXpH32',
  'rz6KGs5IoAOfyeuxtjJDUi9ZE8l1',
  'eR1SSGYh3WPyTtrbpdUVmH17ceV2',
  'WLazmn1CgzeeTtQ7v0I93Qdn5Ey2',
  '5wYUMAtEQCOsApNd7VdsGCiDQ7l2',
  '357Bf1nXUVYa3UPZoBtHdpR6Wjl2',
  'w4WMZejO7PcZF6m4EQUK18ECl493',
  '1X2TmszlFycSmEJ7Pzffe38in5i1',
  'Tpw8uuwDjaYIaJo2vYUgrUNyEgB2',
  'eaNgbm4sF7MOFy0F4oNwNV80fH02',
  '8aEnKLlc5GXbDoNsL6M5CioZBif1',
  'ls1UHjDIpEPTjXnqvYd2nQTDQXj2',
  'QyYW3SqY3BP6h7RHatA6fNhaFjD2',
  'sfZosRxBdZXj0aP33zaGi3jufHm2',
  '3wfAuxdbkFXZqP0PNJkDOAV2Rx13',
  'oFqSCkeiEMfniDWoTmxn4or1qH33',
  'SYnF9rUw0Pcdg82Al3n79GQRjWy2',
  'EIlTKBRWrYYTGUrlOQDv3mA17632',
  'WIGEhRM8zEesqBqnMTDJK4tTmTv1',
  'PGc0LyHADdPZKKCixvHVorc1ThN2',
  '0UOOXBKZwVQaxvcksL6XYCxyb9C2',
  'RIwwbQSdiBY1eXK68C4R1D68O9y2',
  'KDUP9Spy8QZq6Bxayqbgso3wc5d2',
  'boYbvNkVmxZUWJ2dpZuetYiazpu1',
  'CfCgA9zMuAh64hZpcwdmRsLyDHH2',
  '0wHCpKc0GUbyeaZsfgElTUkVkhu1',
  '53iOYAjMODNAKtlWf39gwqutJH02',
  'Y7DBrIq5bhTjl9Tyikq2exudxBG3',
  'Ma5X3ibZmrbPD98j4wB6SqewqZw2',
  'OF8T7tpzrVWKB5Mqg9Rtf0VDoRO2',
  'jB1s2bON9XWw4tSOXi2rX9LrAGa2',
  '3T9oavJKAYUgdGo2SBiAui0exXj1',
  'OeItk0iSoXUZ557xspHzG9RGgih2',
  'Qf7urAfAyXb4UkAVJwzPB6OWysj2',
  've9KE6oTw2T3nqP7dNE8eZOSjKi1',
  'HoySMZLjgjhtfHH2buFU2YjoGYi1',
  'fIuVER76pwSjurEaqdyigjtSZaW2',
  'ZulMToCa1RQiqoFs2OnaRjtLfXu2',
  'MtfHu1YoZNbMgj7Ku37d68pxhAS2',
  '2alOv7lg92ceoQxBeAiZb5plz4C3',
  'sLS5g7HFzSV5H5VmlcY5ME1IrfD3',
  'uddZTBrzUcbACmSTkLUeVUTDZgL2',
  'h84DvhBnKLfgzlthV4Umebh2G0f2',
  'nLW39LHQVbPAJTUmGI3KV5nVFVT2',
  'fjly7sZ99SNQaeNYtvRbZqMtqdP2',
  '11Q4l6EasbMlbNHiDKuz8wmnqWO2',
  'KIZ4GSWf5HR0EKwaoNnOFSNFV9B2',
  'R66ThzWMTwgHA5YJddOcOVOdzM02',
  '2YrupcG1gZOSTfO1WOCUAvy9l3J2',
  'S90lvXswWeXV18rmeV9HztGa5uE2',
  'VDfWLQrIFKeItbbtnHKhrOflCSI2',
  '7NzbvAqrbVe3PoWiLOEFiQ4AmJE3',
  'ROCPXnTaEMMlSEU7iYP6T7ZdqvJ3',
  'mpa5kOGu0sWbyYishVrRWm1cblo1',
  'Cby3v5IoeZgnsGsvER3nc5b6ARj1',
  'pOoQ9zoVCmOPIK6RsvF1WGvQNoX2',
  'VA7ciroDXUbRz7UUBQNS6D6a67d2',
  'SYi7N5Sng1VcnDMVzIeJBhJiu4n1',
  '9YFpGZWlsSRozEBVadKx0TD3lOr1',
  'RDur9M0b9SWuwjrodbYHdxqryw33',
  'db1OruV4iSXVYYUbnH6ms7mOgWf1',
  '03Cz85qYXghzUTE8wnfzOKjKrzu2',
  '1zVAI0ScEYfMiv1d8B6OELVspEs2',
  'z85sNzm3XDVaJOHQK7VsqOgfcbi2',
  'kChTbmGNxAfjW5HiOZXQw6Ytjz62',
  '3yS3lqLEfHdEV20WLPKDCsq5Dp52',
  'x7IgFAKl9PedZdpuu7kMePq0epf2',
  'DiLp3nEDgdcLnigzzy5T1oVEC9j2',
  'gonjFojseIZqgftAmTNT3Qm9svj2',
  'E0L3DKPyimfAPKIGa1IDwDjaPHi2',
  'buHsQy6lfOVuGg1IOpRbCaAhQU03',
  'zPk6NjhvjQSiT9m4phfvxrzqDQv2',
  'WlGXh2JQARfJaLc2LP13pktr1bj2',
  'mNvoXpfG2bbMX7Vul73Z7nqgtLZ2',
  'twHTD4bluRcBzPEpIHoJuSZhNeA2',
  '3TQmWr3C7mV0nGeqvLq6WgzsDcs1',
  'j7cR4wUYaze7bZuWgAtANGwjQFi2',
  'LpMaAvst0pdvbpuKZrZFvHuoy1I2',
  'kvrpmBY4FRNpEYBZOy5bkY6qPu32',
  'cjDbbgNRGDhs7Kj1LFooVIDDuXw2',
  'Af1BTRI4eKcZAeTOfzLrnHu1Vrr2',
  '041HvPubZaW2p9HY7ne8RUKPdvi1',
  'ROCPXnTaEMMlSEU7iYP6T7ZdqvJ3',
  'Cby3v5IoeZgnsGsvER3nc5b6ARj1',
  'KS56zAs3QiPTw8AWCY14cLH9HIY2',
  'OiYmWMZ5w7MEltT188rVAIlITvn1',
  '1LG3u4dUdyPE3m7wfA00CrkMHnC2',
  'WexDJ9GgHuNT7875SyiEIu4bwFp2',
  'u4cR46kNioODDqUK1n99A5fJ4hh2',
  'bBexrElRkmenUeBX8HRHprFOw3w1',
  'Rh0vbluZzWZRdM7vG4LfQCnp2h02',
  'LCya2vwKlhdqLR9m8Zwq4Ify7Zr1',
  '251V30EsE9Yu959VpoxO6tdaBj43',
  'YKhrrNcARAeqRhq80MLlSgUM4Cb2',
  '0AuRQOjIZzOBLq8iO7O7N2DaAsQ2',
  'uyji4flIp0SaJ6OGeAXnY0lVOAi2',
  '2L6yP0fq3negD25zPRocAs0Z2oP2',
  'zhuhzTod2IWsnI801S4UPofPm9R2',
  'lPsDmQmxPBRuyelCgiJ1AOiCDj13',
  'ea7RJkZ5zsSJCYZZC70mITHIjUP2',
  'BFRSRtno7hd24ZDYxVxnQQAMCTW2',
  'mc11TwvhG7aDwK2uR3yh11ypEWo1',
  'OcJckKztGJbjtZTWN0DUQT4UPnZ2',
  '5a8g77Z2MigySFONeGDhhMOPZc12',
  't7JMF3JDafawmQ8GrRfL0vMerUC2',
  'KopLRN36kOTUBcpqiTDV6pBdGg32',
  '8aFndgSgMeV0h8ODQUoAV4As9Ie2',
  'EG7Sily8F6NIOd4eCmLEFjU7Vp72',
  '5CAf3qkz2IduoW9Ms96S8eDNxFd2',
  'zRh6nhlYefcSHYTWnB48m5xYWCB3',
  'DadU89MJsXdFiY35SV7zj1knpz82',
  'nePFOjqU6NX68paTdjs4VyHdufy1',
  '3cqEEFuaw8SKBhOcGyry7AxFUX83',
  'KS56zAs3QiPTw8AWCY14cLH9HIY2',
  'h1odKTxG0RflWQUelsWnnIwXO4q2',
  '1LG3u4dUdyPE3m7wfA00CrkMHnC2',
  'S930iEw4CeNum6IJFjsD6QdP37c2',
  'fCc8ZumZhSaHBBfFKGzcNns3slh1',
  'ZZkbTOKvgBPKW2CKXPFMzvmiLdB3',
  'goCybo3lCyT7LRoCHbg2lJUCi913',
  '2iUbzOAZbkM0iFM5PLtTnAl1H0D3',
  'f1BKyPfj9uVpJBy2vw2Z71AO9483',
  'pQEPKW8B0eencQekJTp8CbR6WpG3',
  'YZh5mwSxoCXKjJVwSZus1DuEmvv1',
  '51SYTzfatSYFDokes1kZjyZysJD3',
  'DeZTqHPQFgTlu8hBRL55mX4OAcg1',
  'PfBdbEVK2qg8aolg7X1FZEv8LYD2',
  'nqaaKjBf3EfRc5Gx6ErLtclU9Ou1',
  'ung1nlHG97gyrsrNXgPgIHHXbaA2',
  'b75nUwDyqSYWs87EQdIL69aBR8v2',
  'N780KDpeuFhuqq1w5H4kboSSNL93',
  'asUGb7hy8dgx1oCBQelq5A9CfLp1',
  'OODI9gWLzpXjOkPa9zNs8Y4qVIa2',
  'Ce7GNQUIkih94cuIk6sm4JgKco92',
  '2CM2UOr2oDdJP6AAEHrqgVacSO43',
  'lArDZRyHCyVSaP1lS6ZMCZgZSH13',
  'kWdwpjSwrBUYndShuNdHqNluBnn2',
  '6iiZMG9smMgv7logIjLbSOiexA63',
  '6F8sJeBTP3WEisaFZSL8YUwvEI02',
  'QcP4BKAZFOg4Ky21TtLpV7GbjWs2',
  'IggtUW5h9lWfEi1DIiX3DDHrxD53',
  'vkbjNT64dvN0B0bQaWnpIX3bXoo2',
  '1RKZ1JWUMucjf6VOCQRqYS7HUw63',
  'iRBEmfaTv8aLEMyXxVQm3Bi4Bc53',
  'OYZbpTsnbIXoFmSzg9uPYV7Muyi2',
  'Q3LOxwoIwIfpCVBWhbfTkhGpGlx2',
  'kYc6NATxwXb6cKjMg7UzcOGvE8F3',
  'HBVbAczYLxb0UlrzCSMMdlUGF532',
  'O4FUuqHyhYPqogAQvkXzen3qG4A2',
  'UnSx2O2nVUO8d44D6UYmlaIBgl82',
  'wVKXa4c8pOabTFQE7M0seVzmT9y1',
  'dmHUGSoXWbQGEhDtzfN0jJ9snQv2',
  'RcfOp6Brlme34jnvASq2uRpdcOz2',
  'n8xqZP5xGhYD9trKEu9t5UdjZVp1',
  'oCZ9CvnTorSJKWvpNUvbmUcNBlH3',
  'cLJyDNHvYyUPBIgByw9Brlnw0an1',
  'mWmUIOmUI5hUAeCGeYM4F0b7BZx2',
  'uxS0W11WOBZDcYFYCdN8Lgn73BV2',
  'SJvGhQosoyMezozbttyTR7EXmhu1',
  'IXo5NdpLY5PWdWh2Ga4IwM7jQql2',
  'vldaUpKxSUOIm82XzfDdIAJNU372',
  'OSvcTGPgi3Yt2oswSfX2Z61A8lC2',
  'RuHYT9apR1Tb1mONcZ5c1KboGLy1',
  'clHzNq9QShO8aJvmIEhGNi8ylGa2',
  'cW0KjtZ8RvVymHaanUXYARg31HF3',
  '7LvvycpRzHenL5hFnhZkwEwiSiC3',
  'LuEUvBrfBOWEjC8ABtY5rSC0RHL2',
  'RKTFDLjGpTReGDu9t4WUytK3hTZ2',
  'uj3N8d0EKgP0HNkOy7lM6wcE9M53',
  'hNJJvZnxXiUAEgwYgqPGCVif1f43',
  'p6eWIm6xiaOC5H2KxDYl4nS4Xa73',
  'KDSe0SfyDlgHeGXip03Nhz8JMGf2',
  'c6Wb0FRuQlNq1WxmW1vYDtxMTom2',
  'KGR9VKC0GiZNvNeb3Uk8jXjHlY93',
  'qMfETovpdXRz4ExZ4C7VTIc9fCN2',
  'cNWz4Pxnvwh3HGXcNWT8Xga09Dp1',
  '0qwnR4bhwfSFlpOM5o8LlVaOF9K2',
  'sPc8ET4RMjS0Binbyf8VAx7KzzE2',
  'fqjuLiDiU4R8CmVagMPw5zOhfbv1',
  'yeaUIOr313MWjoAG2Ftaqa3T7Qn1',
  'Veze7YnQPfWCpM4kdGnGeWYb9y33',
  'x4Kx4Gxf4CYnAmgd7jISbizOcem2',
  'dnktVvKu4bMuCWdeEOvzFBbj9su1',
  'aKPCgjs1jifPLEUEjuxWuUBERgf2',
  'rESLVFKbhDZ8atyMZRKQ3kG3Jfo1',
  '6ZjUyRhnRxS5PohG76ErGUFFX2J3',
  'NblNBAZ6t4bPkL6vgHACJ65YhD53',
  'TL0RKY2ylUXCF6M0n6JwZhCixEj2',
  'AcoGA4D3HCSzTczGuJENrvfVGt33',
  '1H1NPUQLYNM2K1hAu4liAJRNMTB3',
  'nwFKDWjWtSQllrCWFnaStU3HV7u1',
  'QtYO4ZNGfqZpGLWYmBQN01H90r43',
  'GMcxdfgvBWShtPM3pGF2ub1fRFn2',
  '8i01mAyYHFTIgOzIWe17wkKvp2J3',
  '8c9dXKQnCufmWphxTmwinu3wBZD2',
  'NcLIgDP5wEWX4X3Pk9BMpVW75Ka2',
  'Z3wtMQLnHVRUP9EDK2U0imErwAo2',
  'tSd6v2ojpgc2p6ym3wBCPP7F9Ck2',
  'qkw9pPuUR3hz0z8CBjQNrBfCLbE2',
  '0kcwqUtQMaOj4nCTxrkWtISnVSg1',
  'ZgYLNaV0ISfZL5kJOEtIHXBQRRJ3',
  'AIDtGRKxveRXXXTIGpGizH4irwF2',
  'PN93KTPMIFa50vRTP0yyIqFNmbg1',
  'jyCtOUxLYNcRo7Fae07VP9q7nMH3',
  'qel9L3Xd46Zw8LVvYVaujzxuACR2',
  'zlHPtQHjzyduUBu9W21nPnOXXoF3',
  'r8uO3hyUYSZ0ePhOhPrTQzCoi5r2',
  'YusuaoUmQXc6Bc00kFc4b9JTowk2',
  'VKKLP7dApJa3pyDs8NEsIcEvDtm2',
  'IXjQdEN7togW6ADyW4WdAFJcDmZ2',
  'HpUuQy6WC9eNybiqIvXNcU5nYrp1',
  'LEZqwalg9UQ4TBLbtPobaOaDJCj2',
  'ixbDavYhBiZvinXLN8U3koUhuQ52',
  '70g4j4nXkHa1O4rEedNUqKffeoA2',
  'QXrQccv57WhfPyU5k3pFy4dacSq2',
  'ZwQw0rcf3XbNOHVykmc2cGppBFP2',
  'y4vZ1Ugmw9eyz30SgWxZM7oeGNj2',
  'QMdo2cV7NGhWhKFzjVSL1ztuthm2',
  '5Gcn5tJTfhRMVLkbAwWt00FxNMO2',
  'e6KMXmOjABPvTsdIq2zZalmZLdy2',
  'W86AUm9pPHUsxfQL70mqSojKp7W2',
  'nC2iuZqPzNg2ksooiePdVjAY2PM2',
  'WMnhdXZRkaUZv4Gft97h1io2u162',
  'QNfWf4vQdfOUZaxaLksmhG1x9S82',
  'nyubnLlM8ObCNFTPipG0eE5X9K93',
  'dOXz4up5ikNp7PXSNetTodELcsY2',
  'rlVcr9JH38PkWwShAvhcXMEjFyb2',
  'x8ecPXRdZYZLKSTV4mvJ1FZcnVg2',
  'Iv9b89MGw6SDQOudZOFD0eaomv22',
  'iNcxTxlpFyNFYtJAnRPJI82N0H83',
  'xxUJOyY7IMXHcijdYLumHwwA2M63',
  'x9ZkNLsowiYBZ6RGVPl1i3hEN7c2',
  'Q7RQd0qDKaYuAb5b3ZnA5wtVzBi1',
  'nzY9LDMa0NOEMfvHaSFmDF6WbKU2',
  'PEzqRYnMCMeKQtA2wlpENPizBvB2',
  'FNdCrrmzEod0NGFt7qqcpO4Pqyf2',
  'aEDPLVy426fpKe7TThsJLjs8ywp2',
  '1CG2FxH47vNG00zJJbUU2tBGif22',
  'Tv2dMClObxeAdHLJ034y5ByhOCZ2',
  'HAisHf8Og9ZUguD9dkt9tUDuy7k2',
  'Gg1K2sdyBBZhz1JE48kYOGBDlNc2',
  'evvufB83gIWBZmlnij4ac0nY3zg1',
  'idzFYQs78IchuDVKyc1YuJEDzIg1',
  'tzsnmBFvjTMMrhN7AphJkp0B5VW2',
  'MirpB8DFovYvIZNpP7AQNdSPML52',
  'hI7jvX6XqYMWYzCNxRxiyf9G65P2',
  'ODrx9u7eLldTUPvxhq1k4aqJeEE2',
  'zinhUaVzElTN6TQTtyhbiva8PKL2',
  'L5HEgIFrYkOXSUIFLo5H2zSEwF22',
  'CI1FEjKUF7TqUTfV4cKopqQtY0B3',
  'aETNzDdG5Ac5FvfNPysB5k7bx3C2',
  '7yBB1wEPsZcN9dQeErZMShEzFbI3',
  'gml8X1JOMLYOM27F47YpQNDXJUQ2',
];

const MONGODB_URI = process.env.MONGODB_URI;
const CSV_FILE = 'ig_logo_test.csv';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');

  const client = openaiClient.getOpenaiClient();
  const provider = new OpenAI(client, 'gpt-4o');

  async function executeWithImage(imageUrl) {
    const params = {
      prompt: 'Does the following image contain an Instagram logo and a row of objects that might resemble letters? Return either yes or no, along with an explanation.',
      image_urls: [ imageUrl ],
    };
    return await provider.executePrompt(params);
  }

  /*
  const users = await User.find({
    createdAt: { $gt: new Date('2025-03-19') },
    'pictures.0': { $exists: true },
    // description: { $regex: '🧠' },
    shadowBanned: false,
    os: 'ios',
  }).limit(100)
  */
  const users = await User.find({
    _id: { $in: userIds },
  });

  let records = [];
  for (const user of users) {
    const imageUrl = `http://images.prod.boo.dating/${user.pictures[0]}`;
    response = await executeWithImage(imageUrl);
    response.user = user._id;
    response.imageUrl = imageUrl;
    console.log(response);
    records.push(response);
    const csv = stringify(records, { delimiter: ',', header: true });
    await fs.writeFileSync(CSV_FILE, csv, 'utf8');
    console.log(`Processed user ${user._id}`);
  }

  await mongoose.disconnect();
  console.log('Done');
  process.exit();
})();
