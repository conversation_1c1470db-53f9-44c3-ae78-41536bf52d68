const OpenAI = require('openai');

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_KEY,
});

(async () => {
  const response = await deepseek.chat.completions.create({
    model: 'deepseek-chat',
    max_tokens: 8000,
    messages: [
      {
        role: 'user',
        content: `Help me talk with my match on Boo, a personality-based social dating app, in language "ja". Provide 4 topical icebreakers for me to send to my match, formatted as a json array in the following format: { output: ["example 1", "example 2", "example 3", "example 4"] }. Don't assume anything not stated in the following sentences.
      
      Me: <PERSON><PERSON>, 29 M, INTJ, Taurus, Dating/Friends, My location Pune, My interests psychology gadgets philosophy memes dogs
      
      Match: Kate, 39 F, INFJ, Virgo, Dating/Friends, Their location Indonesia, Their interests animals relationshipadvice psychology
      
      Ensure the response is in language "ja"`,
      },
    ],
  });

  console.log(response.choices[0].message);

  /*
    {
    id: '69d35f99-8e39-4a95-bd23-5358bb7424e1',
    object: 'chat.completion',
    created: 1738397058,
    model: 'deepseek-chat',
    choices: [
        {
            index: 0,
            message: {
                role: 'assistant',
                content: '```json\n' +
                    '{\n' +
                    '  "output": [\n' +
                    '    "こんにちは、ケイトさん！心理学に興味があるんですね。最近読んだ本や、印象に残った心理学の理論はありますか？",\n' +
                    '    "動物が好きとのことですが、特にどんな動物がお気に入りですか？私は犬が大好きで、いつか飼いたいと思っています。",\n' +
                    '    "関係のアドバイスに興味があるとのことですが、人間関係で大切にしている価値観や考え方はありますか？",\n' +
                    '    "インドネシアは素敵な場所ですね！旅行や文化について教えてもらえたら嬉しいです。特に訪れてみたい場所はありますか？"\n' +
                    '  ]\n' +
                    '}\n' +
                    '```'
                },
            logprobs: null,
            finish_reason: 'stop'
        }
    ],
    usage: {
        prompt_tokens: 160,
        completion_tokens: 145,
        total_tokens: 305,
        prompt_tokens_details: { cached_tokens: 0 },
        prompt_cache_hit_tokens: 0,
        prompt_cache_miss_tokens: 160
    },
    system_fingerprint: 'fp_3a5770e1b4'
    }
  */
})();
