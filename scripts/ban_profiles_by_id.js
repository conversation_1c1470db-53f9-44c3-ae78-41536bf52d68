const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

const ids = [

];

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    _id: { $in: ids },
    shadowBanned: { $ne: true },
  });
  console.log(`Found ${users.length} users`);

  for (const user of users) {
    await reportLib.shadowBan(user, null, 'spam (banned by cleanup script)', 'using cats to spell social media handle');
  }

  console.log('Done');
  await mongoose.disconnect();
})();
