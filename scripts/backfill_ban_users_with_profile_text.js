const mongoose = require('mongoose');
const reportLib = require('../lib/report');
const BannedInfringingText = require('../models/banned-infringing-text');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI;

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const docs = await BannedInfringingText.find();

  for (const doc of docs) {
    console.log(`Processing ${doc.text}`);
    await reportLib.banUsersWithProfileText(doc.text);
  }

  console.log('Done');
  await mongoose.disconnect();
})();
