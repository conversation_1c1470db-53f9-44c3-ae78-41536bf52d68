const axios = require('axios');
const RecaptchaLog = require('../models/recaptchaLog');

const verifyRecaptcha = async (req, res, next) => {
  try {
    const code = req.body?.captchaToken;
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    if (code && secretKey) {
      const remoteIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.ip;
      const userId = req.user?._id || undefined;
      const email = req.body?.email || undefined;
      const response = await axios.post(
        'https://www.google.com/recaptcha/api/siteverify',
        {
          secret: secretKey,
          response: code,
          remoteip: remoteIp,
        },
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
      );
      const { success, score, action, challenge_ts, hostname, 'error-codes': errorCodes } = response.data;
      await RecaptchaLog.create({
        user: userId,
        email,
        success,
        score,
        action,
        challenge_ts: challenge_ts ? new Date(challenge_ts) : null,
        hostname,
        errorCodes,
      });
    }
    return next();
  } catch (error) {
    console.error("Error verifying captcha:", error.message);
    return next()
  }
};

const verifyTurnstile = async (req, res, next) => {
  try {
    const token = req.body?.turnstileToken;
    const remoteIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.ip;
    const secretKey = process.env.TURNSTILE_SECRET_KEY;
    if (!token || !secretKey) {
      return next();
    }

    let responseData = {};
    const path = req.baseUrl + req.path;
    const userId = req.user?._id || undefined;
    const email = req.body?.email || undefined;
    try {
      const response = await axios.post(
        'https://challenges.cloudflare.com/turnstile/v0/siteverify',
        new URLSearchParams({ secret: secretKey, response: token, remoteip: remoteIp }).toString(),
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
      );
      responseData = response.data;
    } catch (error) {
      console.error("Error verifying Turnstile captcha:", error.message);
      responseData = { success: false, 'error-codes': [error.code || 'unknown_error'] };
    }

    const { success, action, challenge_ts, hostname, 'error-codes': errorCodes, cData } = responseData;
    await RecaptchaLog.create({
      user: userId,
      email,
      success,
      action,
      challenge_ts: challenge_ts ? new Date(challenge_ts) : null,
      hostname,
      errorCodes,
      cData,
      logFrom: 'turnstile',
      turnstileToken: success ? null : token,
      path,
    });

  } catch (error) {
    console.error('Unexpected error in verifyTurnstile', error.message);
  }
  
  return next();
};


module.exports = {
  verifyRecaptcha,
  verifyTurnstile,
};
