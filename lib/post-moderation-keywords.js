const fs = require('fs');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');

const pathToCsv = `${__dirname}/post-moderation-keywords.csv`;
const input = fs.readFileSync(pathToCsv);
const records = parse(input, {
  columns: true,
  skip_empty_lines: true,
  trim: true,
});

const keywordSet = new Set();
for (const record of records) {
  for (const value of Object.values(record)) {
    if (value) {
      keywordSet.add(value.toLowerCase());
    }
  }
}

module.exports = [...keywordSet];
