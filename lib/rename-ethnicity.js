const User = require('../models/user');

async function renameEthnicity(oldName, newName) {
  if (!oldName || !newName) {
    throw new Error('Both oldName and newName are required');
  }

  if (oldName === newName) {
    console.log('oldName and newName are the same, no changes needed');
    return { matchedCount: 0, modifiedCount: 0 };
  }
  
  const res = await User.updateMany(
    {
      $or: [
        { ethnicities: oldName },
        { 'preferences.ethnicities': oldName },
      ]
    },
    [
      {
        $set: {
          ethnicities: {
            $cond: {
              if: { $isArray: "$ethnicities" },
              then: {
                $cond: {
                  if: { $and: [
                    { $in: [oldName, "$ethnicities"] },
                    { $not: { $in: [newName, "$ethnicities"] } }
                  ]},
                  then: {
                    $map: {
                      input: "$ethnicities",
                      in: {
                        $cond: {
                          if: { $eq: ["$$this", oldName] },
                          then: newName,
                          else: "$$this"
                        }
                      }
                    }
                  },
                  else: {
                    $filter: {
                      input: "$ethnicities",
                      cond: { $ne: ["$$this", oldName] }
                    }
                  }
                }
              },
              else: { $ifNull: ["$ethnicities", null] }
            }
          },
          'preferences.ethnicities': {
            $cond: {
              if: { $isArray: "$preferences.ethnicities" },
              then: {
                $cond: {
                  if: { $and: [
                    { $in: [oldName, "$preferences.ethnicities"] },
                    { $not: { $in: [newName, "$preferences.ethnicities"] } }
                  ]},
                  then: {
                    $map: {
                      input: "$preferences.ethnicities",
                      in: {
                        $cond: {
                          if: { $eq: ["$$this", oldName] },
                          then: newName,
                          else: "$$this"
                        }
                      }
                    }
                  },
                  else: {
                    $filter: {
                      input: "$preferences.ethnicities",
                      cond: { $ne: ["$$this", oldName] }
                    }
                  }
                }
              },
              else: { $ifNull: ["$preferences.ethnicities", null] }
            }
          },
        }
      },
    ],
  );

  console.log(`Ethnicity rename operation completed:`);
  console.log(`- Matched ${res.matchedCount} users`);
  console.log(`- Modified ${res.modifiedCount} users`);
  console.log(`- Renamed "${oldName}" to "${newName}"`);

  return res;
}

module.exports = {
  renameEthnicity,
}
