const constants = require('../lib/constants');
const hive = require('../lib/hive');
const OcrModeration = require('../models/ocr-moderation');

const bannedClasses = {
  spam: 3,
  sexual: 3,
  hate: 3,
  violence: 3,
  bullying: 3,
  promotions: 3,
  child_exploitation: 3,
  phone_number: 3,
  redirection: 3,
}

async function findSpamInImage(key, userId) {
  const url = `${constants.IMAGE_DOMAIN}${key}`;
  const moderationLabels = [];
  let isError, errorMessage, isFlagged, flaggedModerationLabel, detectedText;
  try {
    const hiveOutput = await hive.moderateOcr(url);
    for (const output of hiveOutput.body.status[0].response.output) {
      if (output.frame_results.length) {
        detectedText = output.frame_results[0].block_text;
        for (const x of output.frame_results[0].classes) {
          if (x.score > 0) {
            moderationLabels.push(x);
          }
          if (bannedClasses[x.class] && x.score >= bannedClasses[x.class]) {
            isFlagged = true;
            flaggedModerationLabel = x;
          }
        }
      }
    }
  } catch (err) {
    console.log('Unexpected Error in findSpamInImage:', err);
    isError = true;
    errorMessage = err;
  }
  await OcrModeration.create({
    user: userId,
    key,
    url,
    detectedText,
    moderationLabels,
    isFlagged,
    flaggedModerationLabel: flaggedModerationLabel || undefined,
    isError,
    errorMessage,
  });
  return isFlagged ? detectedText : undefined;
}

module.exports = {
  findSpamInImage,
}
