const PersonalityDatabaseChangeTracker = require('../models/personality-database-change-tracker');
const Subcategory = require('../models/subcategory');
const Category = require('../models/category');
const Profile = require('../models/profile');

async function getPersonalityDatabaseMaxIds() {
  const [maxCategory, maxSubcategory, maxProfile] = await Promise.all([
    Category.findOne({}, { id: 1 }).sort({ id: -1 }).lean(),
    Subcategory.findOne({}, { id: 1 }).sort({ id: -1 }).lean(),
    Profile.findOne({}, { id: 1 }).sort({ id: -1 }).lean()
  ]);

  return {
    category: maxCategory?.id ?? 0,
    subcategory: maxSubcategory?.id ?? 0,
    profile: maxProfile?.id ?? 0,
  };
}


async function checkPersonalityDatabaseChanges() {
  const currentMaxIds = await getPersonalityDatabaseMaxIds();

  let maxIdInfo = await PersonalityDatabaseChangeTracker.findOne({ _id: 'singleton' });

  if (!maxIdInfo) {
    newDoc = new PersonalityDatabaseChangeTracker({
      _id: 'singleton',
      highestCategoryId: currentMaxIds.category,
      highestSubcategoryId: currentMaxIds.subcategory,
      highestProfileId: currentMaxIds.profile,
      createdAt: new Date(),
      lastUpdateAt: new Date(),
    });
    await newDoc.save();

    return {
      categories: true,
      subcategories: true,
      profiles: true,
      hasAnyChanges: true,
    };
  }

  const prevValues = {
    highestCategoryId: maxIdInfo.highestCategoryId ?? 0,
    highestSubcategoryId: maxIdInfo.highestSubcategoryId ?? 0,
    highestProfileId: maxIdInfo.highestProfileId ?? 0,
  };

  const changes = {
    categories: currentMaxIds.category > prevValues.highestCategoryId,
    subcategories: currentMaxIds.subcategory > prevValues.highestSubcategoryId,
    profiles: currentMaxIds.profile > prevValues.highestProfileId,
  };
  changes.hasAnyChanges = changes.categories || changes.subcategories || changes.profiles;

  if (changes.hasAnyChanges) {
    maxIdInfo.highestCategoryId = currentMaxIds.category;
    maxIdInfo.highestSubcategoryId = currentMaxIds.subcategory;
    maxIdInfo.highestProfileId = currentMaxIds.profile;
    maxIdInfo.lastUpdateAt = new Date();
    await maxIdInfo.save();
  }

  return changes;
}

module.exports = {
  checkPersonalityDatabaseChanges,
};
