const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const httpErrors = require('../lib/http-errors');
const UserPollBlogs = require('../models/user-poll-blogs')
const Blogs = require('../models/blogs')

module.exports = function () {
  router.patch('/poll', asyncHandler(async (req, res, next) => {
    // validate input
    const user = req.user
    const userId = user._id
    const personalityType = user?.personality?.mbti || null
    const { blogUrl, option } = req.body;

    if (!blogUrl || !userId || !personalityType) {
      return next(httpErrors.invalidInputError());
    }

    const parsedOption = Number(option);
    if (option == null || isNaN(parsedOption) || parsedOption < 0) {
      return next(httpErrors.invalidInputError());
    }

    let userData = await UserPollBlogs.findOne({ blogUrl, userId }) 
    if (userData && userData._id) {
      return next(httpErrors.forbiddenError())
    }

    let blogs = await Blogs.findOne({ url: blogUrl }) 
    if (!(blogs && blogs._id)) {
      return next(httpErrors.invalidInputError())
    }

    await Blogs.updateOne({ url: blogUrl }, { $inc: { [`pollData.${option}.${personalityType}`]: 1 } })
    await UserPollBlogs.create({
      blogUrl,
      userId,
      option,
      mbti: personalityType
    })
    return res.json({});
  }));

  router.get('/user/poll', asyncHandler(async (req, res, next) => {
    const userId = req.user?._id || null
    const { blogUrl } = req.query;
    if (!blogUrl || !userId) {
      return next(httpErrors.invalidInputError());
    }
    const poll = await UserPollBlogs.findOne({ blogUrl, userId }).select({ _id: 0, blogUrl: 1, option: 1, mbti: 1 }).lean()
    return res.json({ poll: poll || {} });
  }));

  return router;
};
