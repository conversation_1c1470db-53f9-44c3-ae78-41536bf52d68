const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const aiFilterSchema = new mongoose.Schema(
  {
    createdBy: { type: String, ref: 'User' },
    filter: { type: String, required: true, trim: true },
    translatedFilter: { type: String, required: true, trim: true },
    filterEmbedding: { type: [Number], required: true },
    usageCount: { type: Number, default: 1 },
    translationCost: { type: Number, default: 0 },
  },
  {
    timestamps: true,
  },
);

aiFilterSchema.index({ filter: 1 });
aiFilterSchema.index({ usageCount: 1 });
aiFilterSchema.index({ translatedFilter: 1 });

// Export schema
const connection = connectionLib.getRecordsConnection() || mongoose;
module.exports = connection.model('AiFilter', aiFilterSchema);
