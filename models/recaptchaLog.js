const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const recaptchaLogSchema = new mongoose.Schema({
  user: { type: String, ref: 'User' },
  email: { type: String },
  success: { type: Boolean, required: true },
  score: { type: Number, required: false },
  action: { type: String, required: false },
  challenge_ts: { type: Date, required: false },
  errorCodes: { type: [String], required: false },
  hostname: { type: String, required: false },
  createdAt: { type: Date, default: Date.now },
  cData: { type: String },
  logFrom: {type: String, enum: ['recaptcha', 'turnstile'], default: 'recaptcha' },
  turnstileToken: { type: String },
  path: { type: String },
});

recaptchaLogSchema.index({ email: 1 });

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('recaptchaLog', recaptchaLogSchema);
