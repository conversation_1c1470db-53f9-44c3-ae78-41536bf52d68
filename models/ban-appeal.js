const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  bannedReasons: { type: [{ type: String }], default: undefined },
  banNoticeReason: { type: String },
  comment: { type: String },
  reviewedBy: { type: String, ref: 'User' },
  reviewedAt: { type: Date },
  decision: { type: String, enum: [ 'approved', 'rejected', 'dismissed' ] },
  notes: { type: String },
});

schema.index({
  decision: 1,
  createdAt: 1,
});

schema.index({
  createdAt: 1,
});

schema.index({
  user: 1,
});

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('BanAppeal', schema);
