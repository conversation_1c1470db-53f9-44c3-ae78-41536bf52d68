const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  userId: { type: String, ref: 'User' },
  chatId: { type: mongoose.Schema.Types.ObjectId, ref: 'Chat' },
  prompt: { type: String },
  output: { type: String },
  formattedOutputPrompts: { type: mongoose.Mixed },
  result: { type: String },
  meetUp: { type: Boolean },
  contactExchange: { type: Boolean },
  transactions: { type: Boolean },
  promptTokens: { type: Number },
  outputTokens: { type: Number },
  isError: { type: Boolean },
  errorMessage: { type: String },
  cost: { type: Number },
  model: { type: String },
  processingTime: { type: Number },
  inputText: { type: String },
});

schema.index({
  createdAt: 1,
});

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('ChatAnalysisYourTurn', schema);
