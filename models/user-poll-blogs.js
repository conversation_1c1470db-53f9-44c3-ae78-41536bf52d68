const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');
const { validMbti } = require('../lib/personality');

const schema = new mongoose.Schema({
  blogUrl: { type: String },
  userId: { type: String, ref: 'User' },
  option: { type: Number },
  mbti: { type: String, enum: validMbti },
  createdAt: { type: Date, default: Date.now },
});

schema.index({ blogUrl: 1, userId: 1 });

// Export schema =====================================================================================================================================================================
const conn = connectionLib.getBlogsConnection() || mongoose;
module.exports = conn.model('userPollBlogs', schema);
