const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  user: { type: String, ref: 'User' },
  key: { type: String },
  url: { type: String },
  detectedText: { type: String },
  moderationLabels: [Object],
  isFlagged: { type: Boolean },
  flaggedModerationLabel: Object,
  isError: { type: Boolean },
  errorMessage: { type: String },
});

schema.index({
  createdAt: 1,
});
schema.index({
  key: 1,
});
schema.index({
  user: 1,
});

// Export schema =======================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('OcrModeration', schema);
