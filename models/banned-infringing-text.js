const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  text: { type: String, unique: true },
  count: { type: Number },
});

// remove after 90 days
schema.index(
  { createdAt: 1 },
  { expireAfterSeconds: 86400 * 90 },
);

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('BannedInfringingText', schema);
