const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  prompt: { type: String },
  output: { type: String },
  promptTokens: { type: Number },
  outputTokens: { type: Number },
  isError: { type: Boolean },
  errorMessage: { type: String },
  cost: { type: Number },
  model: { type: String },
  processingTime: { type: Number },
  user: { type: String },
  imageUrl: { type: String },
  containsSpam: { type: Boolean },
});

schema.index({
  user: 1,
});
schema.index({
  createdAt: 1,
});

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('DetectInstagramSpamInImage', schema);
