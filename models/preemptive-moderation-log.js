const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');
const openaiSchema = require('./openai-schema');

/*
This schema is similar to the Report schema.
The Report schema is used for user reports and auto reports where we have probable cause of a violation.
This schema is used to log preemptive moderation of user profiles where we do not have probable cause.
*/

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  reportedUser: { type: String, ref: 'User' },
  reportedBy: { type: String, ref: 'User' },
  reason: [{
    type: String,
    trim: true,
    maxlength: 2000,
  }],
  comment: {
    type: String,
    default: '',
    trim: true,
    maxlength: 10000,
  },
  status: {
    type: String,
    enum: ['needs review', 'dismissed', 'verified'],
    default: 'needs review',
  },
  handledBy: { type: String, ref: 'User' },
  adminNotes: { type: String },
  messages: [{}],
  scammerImageDetection: {
    fullMatchingImages: [{
      url: { type: String },
      score: { type: Number },
    }],
    partialMatchingImages: [{
      url: { type: String },
      score: { type: Number },
    }],
  },
  profilePictureSameAsBannedUser: { type: String, ref: 'User' },
  openai: openaiSchema,
  openaiFollowUp: [openaiSchema],
});

schema.index({
  reportedUser: 1,
});
schema.index({
  createdAt: 1,
});

schema.index(
  { 'openai.banReason': 1, createdAt: 1 },
  {
    partialFilterExpression: { 'openai.banReason': 'Spam, Promotion or Solicitation' },
  },
);

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('PreemptiveModerationLog', schema);
