const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    user: { type: String, ref: 'User', required: true },
    merged_embedding: { type: [Number], required: true },
    latitude: { type: Number },
    longitude: { type: Number },
    countryCode: { type: String, trim: true, maxlength: 500 },
    pictureEmbeddings: [
      {
        _id: false,
        key: { type: String, required: true },
        embedding: { type: [Number], required: true },
      },
    ],
    hideFromVisionSearch: { type: Boolean, default: undefined },
    source: { type: String, default: undefined }, // This field is used to track if record is created without backfill script, should always be undefined once backfill is complete
  },
  {
    timestamps: true,
  },
);

schema.index({ user: 1 });

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('UserEmbeddings', schema);
