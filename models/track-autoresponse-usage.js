const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  topic: { type: String },
  issue: { type: String },
  subIssue: { type: String },
  outcome: { type: String },
  messageToSupport: { type: String },
  rating: { type: Number, default: undefined },
  feedback: { type: String, default: undefined },
});

schema.index({ user: 1 });

// Export schema =======================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('TrackAutoreponseUsage', schema);
