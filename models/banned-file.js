const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  user: { type: String, ref: 'User' },
  bannedImages: {
    type: [
      {
        _id: false,
        imageHash: { type: String },
        imageKey: { type: String },
      },
    ],
  },
});

schema.index({
  user: 1,
});

schema.index({ 'bannedImages.imageHash': 1 });

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('BannedFile', schema);
