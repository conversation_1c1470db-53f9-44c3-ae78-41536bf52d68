const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  productId: { type: String },
  currency: { type: String },
  price: { type: Number },
  revenue: { type: Number },
});

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('PurchasePremiumResponse', schema);
