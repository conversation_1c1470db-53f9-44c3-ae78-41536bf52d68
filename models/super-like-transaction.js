const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  user: { type: String, ref: 'User' },
  freeSuperLoveTransactionAmount: { type: Number },
  freeSuperLoveNewBalance: { type: Number },
  paidSuperLoveTransactionAmount: { type: Number },
  paidSuperLoveNewBalance: { type: Number },
  description: { type: String },
});

schema.index({
  user: 1,
  createdAt: -1,
});

// Export schema =======================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('SuperLikeTransaction', schema);
