const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const exclusionListDetailedSchema = new mongoose.Schema({
  user: { type: String, ref: 'User', unique: true },
  likedList: { type: [{ type: String }], default: [] },
  blockedList: { type: [{ type: String }], default: [] },
});

exclusionListDetailedSchema.index({
  user: 1,
});

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('ExclusionListDetailed', exclusionListDetailedSchema);
