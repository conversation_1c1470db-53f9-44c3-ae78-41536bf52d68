const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  user: { type: String },
  verificationPhoto: { type: String },
  review1: {
    queue: { type: Number },
    reviewedBy: { type: String },
    reviewedAt: { type: Date },
    decision: { type: String },
  },
  review2: {
    queue: { type: Number },
    reviewedBy: { type: String },
    reviewedAt: { type: Date },
    decision: { type: String },
  },
  reviewFinal: {
    required: { type: Boolean },
    reviewedBy: { type: String },
    reviewedAt: { type: Date },
    decision: { type: String },
  },
  finalDecision: { type: String },
});

schema.index({
  user: 1,
  createdAt: 1,
});
schema.index({
  'review1.queue': 1,
  'review1.decision': 1,
});
schema.index({
  'review2.queue': 1,
  'review2.decision': 1,
});
schema.index({
  'reviewFinal.required': 1,
  'reviewFinal.decision': 1,
});

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('ScammerCleanup', schema);
