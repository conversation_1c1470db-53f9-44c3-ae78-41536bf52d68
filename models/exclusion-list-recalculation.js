const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const exclusionListRecalculationSchema = new mongoose.Schema({
  user: { type: String, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  exclusionListLengthBefore: { type: Number },
  exclusionListLengthAfter: { type: Number },
});

let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('ExclusionListRecalculation', exclusionListRecalculationSchema);
